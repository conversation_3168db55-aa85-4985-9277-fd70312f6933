'use client';

import { useState, useEffect, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useImmer } from 'use-immer';
import { Plus, Trash2, Package, FileText, Eye, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { SupplierSelector } from '@/components/ui/supplier-selector';
import { ProductSelector } from '@/components/ui/product-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { AdvancedBatchNumberInput } from '@/components/ui/advanced-batch-number-input';
import { BatchValidationSummary } from '@/components/batch-validation/batch-validation-summary';
import { GoodsReceiptFormData, CreateGoodsReceiptDto, QualityControlStatus } from '@/types/goods-receipt';
import { PurchaseOrder, PurchaseOrderStatus } from '@/types/purchase-order';
import { usePurchaseOrders } from '@/hooks/usePurchaseOrders';
import { formatCurrency } from '@/lib/utils';

// Constants for Select options
const NO_PURCHASE_ORDER = '__NO_PURCHASE_ORDER__';
import { toast } from 'sonner';

interface GoodsReceiptFormProps {
  initialData?: Partial<GoodsReceiptFormData>;
  onSubmit: (data: CreateGoodsReceiptDto) => Promise<void>;
  isSubmitting?: boolean;
  mode?: 'create' | 'edit';
}

const goodsReceiptItemSchema = z.object({
  productId: z.string().min(1, 'Produk harus dipilih'),
  unitId: z.string().min(1, 'Unit harus dipilih'),
  quantityOrdered: z.number().optional(),
  quantityReceived: z.number().min(1, 'Jumlah diterima harus lebih dari 0'),
  quantityAccepted: z.number().optional(),
  quantityRejected: z.number().optional(),
  unitPrice: z.number().min(0, 'Harga unit tidak boleh negatif'),
  batchNumber: z.string().optional(),
  expiryDate: z.string().optional(),
  manufacturingDate: z.string().optional(),
  storageLocation: z.string().optional(),
  storageCondition: z.string().optional(),
  conditionOnReceipt: z.string().optional(),
  damageNotes: z.string().optional(),
  qualityNotes: z.string().optional(),
  notes: z.string().optional(),
  // Product substitution fields
  isSubstitution: z.boolean().optional(),
  originalProductId: z.string().optional(),
  substitutionReason: z.string().optional(),
  substitutionApprovedBy: z.string().optional(),
  substitutionApprovedAt: z.string().optional(),
  substitutionNotes: z.string().optional(),
}).refine((data) => {
  // If quantityOrdered is provided (from PO), received quantity should not exceed it
  if (data.quantityOrdered && data.quantityOrdered > 0) {
    return data.quantityReceived <= data.quantityOrdered;
  }
  return true;
}, {
  message: 'Kuantitas yang diterima tidak boleh melebihi kuantitas yang dipesan',
  path: ['quantityReceived'],
}).refine((data) => {
  // Manufacturing date should not be in the future
  if (data.manufacturingDate) {
    const mfgDate = new Date(data.manufacturingDate);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today
    return mfgDate <= today;
  }
  return true;
}, {
  message: 'Tanggal produksi tidak boleh di masa depan',
  path: ['manufacturingDate'],
}).refine((data) => {
  // Expiry date should be after manufacturing date
  if (data.manufacturingDate && data.expiryDate) {
    const mfgDate = new Date(data.manufacturingDate);
    const expDate = new Date(data.expiryDate);
    return expDate > mfgDate;
  }
  return true;
}, {
  message: 'Tanggal kadaluarsa harus setelah tanggal produksi',
  path: ['expiryDate'],
});

const goodsReceiptFormSchema = z.object({
  purchaseOrderId: z.string().optional(),
  supplierId: z.string().min(1, 'Supplier harus dipilih'),
  receiptDate: z.string().min(1, 'Tanggal penerimaan harus diisi'),
  deliveryDate: z.string().optional(),
  invoiceNumber: z.string().optional(),
  deliveryNote: z.string().optional(),
  deliveredBy: z.string().optional(),
  receivedBy: z.string().optional(),
  deliveryCondition: z.string().optional(),
  inspectionDate: z.string().optional(),
  inspectionBy: z.string().optional(),
  qualityStatus: z.nativeEnum(QualityControlStatus).optional(),
  qualityNotes: z.string().optional(),
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
  items: z.array(goodsReceiptItemSchema).min(1, 'Minimal harus ada satu item'),
});

type GoodsReceiptFormValues = z.infer<typeof goodsReceiptFormSchema>;

export function GoodsReceiptForm({
  initialData,
  onSubmit,
  isSubmitting = false,
  mode = 'create',
}: GoodsReceiptFormProps) {
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [totalAmount, setTotalAmount] = useImmer(0);
  const [isPOModalOpen, setIsPOModalOpen] = useState(false);
  const [substitutionDialog, setSubstitutionDialog] = useState<{
    isOpen: boolean;
    itemIndex: number;
    originalProductId: string;
  }>({
    isOpen: false,
    itemIndex: -1,
    originalProductId: '',
  });

  // Get purchase orders for the selected supplier
  const { data: purchaseOrdersResponse } = usePurchaseOrders({
    supplierId: initialData?.supplierId,
    status: PurchaseOrderStatus.APPROVED,
    limit: 100,
  });

  const form = useForm<GoodsReceiptFormValues>({
    resolver: zodResolver(goodsReceiptFormSchema),
    defaultValues: {
      purchaseOrderId: initialData?.purchaseOrderId || '',
      supplierId: initialData?.supplierId || '',
      receiptDate: initialData?.receiptDate || new Date().toISOString().split('T')[0],
      deliveryDate: initialData?.deliveryDate || '',
      invoiceNumber: initialData?.invoiceNumber || '',
      deliveryNote: initialData?.deliveryNote || '',
      deliveredBy: initialData?.deliveredBy || '',
      receivedBy: initialData?.receivedBy || '',
      deliveryCondition: initialData?.deliveryCondition || '',
      inspectionDate: '',
      inspectionBy: '',
      qualityStatus: undefined,
      qualityNotes: '',
      notes: initialData?.notes || '',
      internalNotes: initialData?.internalNotes || '',
      items: initialData?.items || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Calculate total amount when items change
  useEffect(() => {
    const items = form.watch('items');
    const total = items.reduce((sum, item) => {
      return sum + (item.quantityReceived * item.unitPrice);
    }, 0);
    setTotalAmount(total);
  }, [form.watch('items'), setTotalAmount]);

  // Clear purchase order selection and reset form
  const clearPurchaseOrderSelection = useCallback(() => {
    setSelectedPurchaseOrder(null);
    form.setValue('purchaseOrderId', '');

    // Re-enable supplier selection by clearing the field
    form.setValue('supplierId', '');

    // Clear auto-populated notes if they contain PO reference
    const currentNotes = form.getValues('notes');
    if (currentNotes && currentNotes.includes('Purchase Order:')) {
      form.setValue('notes', '');
    }

    // Clear delivery date and other auto-populated fields
    form.setValue('deliveryDate', '');
    form.setValue('deliveredBy', '');

    toast.info('Purchase Order selection telah dihapus, supplier dapat dipilih kembali');
  }, [form]);

  // Load purchase order items when PO is selected
  const handlePurchaseOrderChange = useCallback((purchaseOrderId: string) => {
    if (!purchaseOrderId) {
      setSelectedPurchaseOrder(null);
      return;
    }

    const purchaseOrder = purchaseOrdersResponse?.data.find(po => po.id === purchaseOrderId);
    if (purchaseOrder) {
      setSelectedPurchaseOrder(purchaseOrder);

      // Auto-populate and lock supplier field
      form.setValue('supplierId', purchaseOrder.supplierId);

      // Auto-populate delivery information from purchase order
      if (purchaseOrder.expectedDelivery) {
        form.setValue('deliveryDate', new Date(purchaseOrder.expectedDelivery).toISOString().split('T')[0]);
      }

      // Auto-populate delivery information
      if (purchaseOrder.deliveryAddress) {
        form.setValue('deliveredBy', purchaseOrder.deliveryContact || '');
      }

      // Auto-populate notes if available
      if (purchaseOrder.deliveryNotes) {
        form.setValue('notes', `PO: ${purchaseOrder.orderNumber}\n${purchaseOrder.deliveryNotes}`);
      } else {
        form.setValue('notes', `Penerimaan untuk Purchase Order: ${purchaseOrder.orderNumber}`);
      }

      // Clear existing items and add PO items
      form.setValue('items', []);

      // Add items with enhanced auto-population
      purchaseOrder.items.forEach((poItem) => {
        const remainingQuantity = poItem.quantityOrdered - poItem.quantityReceived;

        append({
          productId: poItem.productId,
          unitId: poItem.unitId,
          quantityOrdered: poItem.quantityOrdered,
          quantityReceived: remainingQuantity > 0 ? remainingQuantity : 0, // Auto-fill remaining quantity
          quantityAccepted: 0,
          quantityRejected: 0,
          unitPrice: poItem.unitPrice,
          batchNumber: '',
          expiryDate: '',
          manufacturingDate: '',
          storageLocation: '',
          storageCondition: '',
          conditionOnReceipt: 'good',
          damageNotes: '',
          qualityNotes: '',
          notes: poItem.notes || '',
          // Substitution fields
          isSubstitution: false,
          originalProductId: '',
          substitutionReason: '',
          substitutionApprovedBy: '',
          substitutionApprovedAt: '',
          substitutionNotes: '',
        });
      });

      // Show success message
      toast.success(`Purchase Order ${purchaseOrder.orderNumber} berhasil dimuat dengan ${purchaseOrder.items.length} item`);
    }
  }, [purchaseOrdersResponse, append, form]);

  const handleAddItem = useCallback(() => {
    append({
      productId: '',
      unitId: '',
      quantityOrdered: undefined,
      quantityReceived: 0,
      quantityAccepted: 0,
      quantityRejected: 0,
      unitPrice: 0,
      batchNumber: '',
      expiryDate: '',
      manufacturingDate: '',
      storageLocation: '',
      storageCondition: '',
      conditionOnReceipt: 'good',
      damageNotes: '',
      qualityNotes: '',
      notes: '',
      // Substitution fields
      isSubstitution: false,
      originalProductId: '',
      substitutionReason: '',
      substitutionApprovedBy: '',
      substitutionApprovedAt: '',
      substitutionNotes: '',
    });
  }, [append]);

  const handleRemoveItem = useCallback((index: number) => {
    remove(index);
  }, [remove]);

  // Handle substitution request
  const handleSubstitutionRequest = useCallback((itemIndex: number, originalProductId: string) => {
    setSubstitutionDialog({
      isOpen: true,
      itemIndex,
      originalProductId,
    });
  }, []);

  // Collect batch validation summaries
  const getBatchValidationSummaries = useCallback(() => {
    const items = form.watch('items');
    const summaries = items
      .filter(item => item.batchNumber && item.batchNumber.trim() !== '')
      .map((item, index) => {
        // Simple validation for demo - in real implementation, this would use the validation hook
        const batchNumber = item.batchNumber || '';
        const isValidFormat = batchNumber.length >= 3;
        const hasWarning = batchNumber.length < 6;
        const isOverallValid = isValidFormat && !hasWarning;

        return {
          batchNumber,
          productName: `Item ${index + 1}`, // In real implementation, get from product data
          isValid: isOverallValid,
          level: (!isValidFormat ? 'error' : hasWarning ? 'warning' : 'success') as 'error' | 'warning' | 'success',
          message: !isValidFormat
            ? 'Format batch number tidak valid'
            : hasWarning
              ? 'Batch number terlalu pendek, disarankan minimal 6 karakter'
              : 'Batch number valid',
          details: {
            formatValid: isValidFormat,
            uniqueValid: true, // Would be checked via API
            bpomCompliant: batchNumber.length >= 6,
            dateAligned: true, // Would be validated against dates
            validationLevel: isValidFormat ? 'STANDARD' : 'BASIC',
          }
        };
      });

    return summaries;
  }, [form]);

  const handleFormSubmit = async (data: GoodsReceiptFormValues) => {
    try {
      const submitData: CreateGoodsReceiptDto = {
        ...data,
        items: data.items.map(item => ({
          productId: item.productId,
          unitId: item.unitId,
          quantityOrdered: item.quantityOrdered,
          quantityReceived: item.quantityReceived,
          unitPrice: item.unitPrice,
          batchNumber: item.batchNumber || undefined,
          expiryDate: item.expiryDate || undefined,
          manufacturingDate: item.manufacturingDate || undefined,
          storageLocation: item.storageLocation || undefined,
          conditionOnReceipt: item.conditionOnReceipt || undefined,
          notes: item.notes || undefined,
        })),
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting goods receipt:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Header Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Informasi Penerimaan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Supplier Selection */}
              <FormField
                control={form.control}
                name="supplierId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier *</FormLabel>
                    <FormControl>
                      <SupplierSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Pilih supplier..."
                        disabled={!!selectedPurchaseOrder}
                      />
                    </FormControl>
                    <FormDescription>
                      {selectedPurchaseOrder ? (
                        <span className="text-blue-600 text-xs">
                          🔒 Supplier dikunci dari Purchase Order {selectedPurchaseOrder.orderNumber}
                        </span>
                      ) : (
                        <span>&nbsp;</span>
                      )}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Purchase Order Selection */}
              <FormField
                control={form.control}
                name="purchaseOrderId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purchase Order</FormLabel>
                    <Select
                      value={field.value || NO_PURCHASE_ORDER}
                      onValueChange={(value) => {
                        const actualValue = value === NO_PURCHASE_ORDER ? '' : value;
                        field.onChange(actualValue);
                        handlePurchaseOrderChange(actualValue);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih purchase order untuk auto-populate..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-[300px]">
                        <SelectItem value={NO_PURCHASE_ORDER}>Tanpa Purchase Order</SelectItem>
                        {purchaseOrdersResponse?.data
                          ?.filter(po => po.status === 'APPROVED' || po.status === 'ORDERED' || po.status === 'PARTIALLY_RECEIVED')
                          ?.map((po) => {
                            const totalItems = po.items.length;
                            const pendingItems = po.items.filter(item =>
                              item.quantityOrdered > item.quantityReceived
                            ).length;
                            const orderDate = new Date(po.orderDate).toLocaleDateString('id-ID');
                            const totalAmount = formatCurrency(po.totalAmount);

                            return (
                              <SelectItem key={po.id} value={po.id} className="py-3">
                                <div className="flex flex-col gap-1">
                                  <div className="font-medium">
                                    {po.orderNumber} - {po.supplier.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {orderDate} • {totalAmount} • {pendingItems}/{totalItems} item pending
                                  </div>
                                  {po.expectedDelivery && (
                                    <div className="text-xs text-blue-600">
                                      Est. Pengiriman: {new Date(po.expectedDelivery).toLocaleDateString('id-ID')}
                                    </div>
                                  )}
                                </div>
                              </SelectItem>
                            );
                          })}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {selectedPurchaseOrder ? (
                        <span className="text-green-600">
                          ✓ Data dari PO {selectedPurchaseOrder.orderNumber} telah dimuat
                        </span>
                      ) : (
                        'Pilih purchase order untuk mengisi item secara otomatis'
                      )}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Receipt Date */}
              <FormField
                control={form.control}
                name="receiptDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Penerimaan *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Delivery Date */}
              <FormField
                control={form.control}
                name="deliveryDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Pengiriman</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Invoice Number */}
              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Invoice</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nomor invoice..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Delivery Note */}
              <FormField
                control={form.control}
                name="deliveryNote"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Surat Jalan</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nomor surat jalan..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Delivered By */}
              <FormField
                control={form.control}
                name="deliveredBy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dikirim Oleh</FormLabel>
                    <FormControl>
                      <Input placeholder="Nama pengirim..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Received By */}
              <FormField
                control={form.control}
                name="receivedBy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Diterima Oleh</FormLabel>
                    <FormControl>
                      <Input placeholder="Nama penerima..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Delivery Condition */}
            <FormField
              control={form.control}
              name="deliveryCondition"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kondisi Pengiriman</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih kondisi pengiriman..." />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="good">Baik</SelectItem>
                      <SelectItem value="damaged">Rusak</SelectItem>
                      <SelectItem value="partial">Sebagian Rusak</SelectItem>
                      <SelectItem value="incomplete">Tidak Lengkap</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Items Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Item Penerimaan
                </CardTitle>
                {selectedPurchaseOrder && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>•</span>
                    <span>Dari PO: {selectedPurchaseOrder.orderNumber}</span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setIsPOModalOpen(true)}
                      className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Lihat Detail
                    </Button>
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={clearPurchaseOrderSelection}
                      className="h-6 px-2 text-xs"
                    >
                      Reset
                    </Button>
                  </div>
                )}
              </div>
              <Button type="button" onClick={handleAddItem} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Tambah Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Batch Validation Summary */}
            {getBatchValidationSummaries().length > 0 && (
              <div className="mb-4">
                <BatchValidationSummary
                  items={getBatchValidationSummaries()}
                  className="mb-4"
                />
              </div>
            )}

            {fields.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Belum ada item. Klik "Tambah Item" untuk menambahkan item penerimaan.
              </div>
            ) : (
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Produk</TableHead>
                      <TableHead>Batch & Tanggal</TableHead>
                      <TableHead className="text-center">
                        Qty Diterima
                        {selectedPurchaseOrder && (
                          <div className="text-xs font-normal text-muted-foreground">
                            (dari PO)
                          </div>
                        )}
                      </TableHead>
                      <TableHead>Storage & Kondisi</TableHead>
                      <TableHead className="text-right">Harga</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead className="text-center">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.map((field, index) => (
                      <GoodsReceiptItemRow
                        key={field.id}
                        index={index}
                        form={form}
                        onRemove={() => handleRemoveItem(index)}
                        selectedPurchaseOrder={selectedPurchaseOrder}
                        onSubstitutionRequest={handleSubstitutionRequest}
                      />
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            )}

            {/* Purchase Order Summary */}
            {selectedPurchaseOrder && (
              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900">Purchase Order Summary</h4>
                    <div className="text-sm text-blue-700 mt-1">
                      <div>PO Number: {selectedPurchaseOrder.orderNumber}</div>
                      <div>Supplier: {selectedPurchaseOrder.supplier.name}</div>
                      <div>Order Date: {new Date(selectedPurchaseOrder.orderDate).toLocaleDateString('id-ID')}</div>
                      {selectedPurchaseOrder.expectedDelivery && (
                        <div>Expected Delivery: {new Date(selectedPurchaseOrder.expectedDelivery).toLocaleDateString('id-ID')}</div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-blue-700">PO Total</div>
                    <div className="text-lg font-bold text-blue-900">{formatCurrency(selectedPurchaseOrder.totalAmount)}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Total */}
            {fields.length > 0 && (
              <div className="flex justify-end mt-4 pt-4 border-t">
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Total Penerimaan</div>
                  <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quality Control Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Quality Control & Inspeksi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="inspectionDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Inspeksi</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        placeholder="Pilih tanggal inspeksi..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="qualityStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status Quality Control</FormLabel>
                    <Select
                      value={field.value || 'NONE'}
                      onValueChange={(value) => field.onChange(value === 'NONE' ? undefined : value)}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih status QC..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="NONE">Belum Ditentukan</SelectItem>
                        <SelectItem value="PENDING">Pending</SelectItem>
                        <SelectItem value="PASSED">Lulus</SelectItem>
                        <SelectItem value="FAILED">Gagal</SelectItem>
                        <SelectItem value="CONDITIONAL">Bersyarat</SelectItem>
                        <SelectItem value="EXEMPTED">Dikecualikan</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="qualityNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan Quality Control</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan hasil inspeksi quality control..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Catatan detail tentang hasil inspeksi dan quality control
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Notes Section */}
        <Card>
          <CardHeader>
            <CardTitle>Catatan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan Umum</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan tambahan tentang penerimaan barang..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="internalNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan Internal</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan internal untuk tim..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Catatan ini hanya untuk internal dan tidak akan terlihat di dokumen eksternal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button type="submit" disabled={isSubmitting} size="lg">
            {isSubmitting ? 'Menyimpan...' : mode === 'create' ? 'Buat Penerimaan' : 'Simpan Perubahan'}
          </Button>
        </div>
      </form>

      {/* Purchase Order Quick View Modal */}
      <Dialog open={isPOModalOpen} onOpenChange={setIsPOModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Detail Purchase Order
            </DialogTitle>
            <DialogDescription>
              Informasi lengkap Purchase Order {selectedPurchaseOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>

          {selectedPurchaseOrder && (
            <div className="space-y-6">
              {/* PO Header Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-600">Nomor PO:</span>
                    <div className="font-semibold">{selectedPurchaseOrder.orderNumber}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">Supplier:</span>
                    <div className="font-semibold">{selectedPurchaseOrder.supplier.name}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">Status:</span>
                    <div className="font-semibold text-blue-600">{selectedPurchaseOrder.status}</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-600">Tanggal Order:</span>
                    <div>{new Date(selectedPurchaseOrder.orderDate).toLocaleDateString('id-ID')}</div>
                  </div>
                  {selectedPurchaseOrder.expectedDelivery && (
                    <div>
                      <span className="text-sm font-medium text-gray-600">Est. Pengiriman:</span>
                      <div>{new Date(selectedPurchaseOrder.expectedDelivery).toLocaleDateString('id-ID')}</div>
                    </div>
                  )}
                  <div>
                    <span className="text-sm font-medium text-gray-600">Total:</span>
                    <div className="font-bold text-lg">{formatCurrency(selectedPurchaseOrder.totalAmount)}</div>
                  </div>
                </div>
              </div>

              {/* PO Items */}
              <div>
                <h4 className="font-semibold mb-3">Item Purchase Order</h4>
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead>Produk</TableHead>
                        <TableHead className="text-center">Qty Dipesan</TableHead>
                        <TableHead className="text-center">Qty Diterima</TableHead>
                        <TableHead className="text-center">Sisa</TableHead>
                        <TableHead className="text-right">Harga</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedPurchaseOrder.items.map((item, index) => {
                        const remaining = item.quantityOrdered - item.quantityReceived;
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{item.product.name}</div>
                                <div className="text-sm text-gray-500">
                                  {item.product.code} • {item.unit.name}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-center">{item.quantityOrdered}</TableCell>
                            <TableCell className="text-center">{item.quantityReceived}</TableCell>
                            <TableCell className="text-center">
                              <span className={remaining > 0 ? 'text-orange-600 font-medium' : 'text-green-600'}>
                                {remaining}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                            <TableCell className="text-right">{formatCurrency(item.totalPrice)}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Additional Information */}
              {(selectedPurchaseOrder.notes || selectedPurchaseOrder.deliveryNotes) && (
                <div className="space-y-3">
                  <h4 className="font-semibold">Catatan</h4>
                  {selectedPurchaseOrder.notes && (
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="text-sm font-medium text-blue-800 mb-1">Catatan Umum:</div>
                      <div className="text-sm text-blue-700">{selectedPurchaseOrder.notes}</div>
                    </div>
                  )}
                  {selectedPurchaseOrder.deliveryNotes && (
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="text-sm font-medium text-green-800 mb-1">Catatan Pengiriman:</div>
                      <div className="text-sm text-green-700">{selectedPurchaseOrder.deliveryNotes}</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Enhanced Product Substitution Dialog */}
      <AlertDialog open={substitutionDialog.isOpen} onOpenChange={(open) =>
        setSubstitutionDialog(prev => ({ ...prev, isOpen: open }))
      }>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              ⚠️ Konfirmasi Substitusi Produk
            </AlertDialogTitle>
            <AlertDialogDescription>
              Anda akan mengubah produk yang telah dipilih dari Purchase Order.
              Tindakan ini memerlukan dokumentasi yang tepat untuk kepatuhan BPOM.
            </AlertDialogDescription>

            <div className="space-y-3 mt-4">
              <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                <div className="text-sm font-medium text-orange-800 mb-2">
                  Persyaratan Substitusi:
                </div>
                <ul className="text-xs text-orange-700 space-y-1">
                  <li>• Produk pengganti harus terdaftar BPOM</li>
                  <li>• Kandungan aktif dan kekuatan harus setara</li>
                  <li>• Dokumentasi alasan substitusi wajib</li>
                  <li>• Persetujuan apoteker diperlukan</li>
                </ul>
              </div>

              <div className="text-xs text-gray-600">
                Dengan melanjutkan, Anda menyatakan bahwa substitusi ini sesuai dengan
                standar farmasi dan regulasi BPOM yang berlaku.
              </div>
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                const itemIndex = substitutionDialog.itemIndex;
                const originalProductId = substitutionDialog.originalProductId;

                // Mark item as substitution
                form.setValue(`items.${itemIndex}.isSubstitution`, true);
                form.setValue(`items.${itemIndex}.originalProductId`, originalProductId);
                form.setValue(`items.${itemIndex}.substitutionReason`, 'supplier_substitution');
                form.setValue(`items.${itemIndex}.substitutionApprovedBy`, 'current_user'); // TODO: Get actual user ID
                form.setValue(`items.${itemIndex}.substitutionApprovedAt`, new Date().toISOString());

                // Close dialog
                setSubstitutionDialog({ isOpen: false, itemIndex: -1, originalProductId: '' });

                toast.success('Substitusi produk disetujui. Produk dapat diubah.');
              }}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Setuju & Lanjutkan
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Form>
  );
}

// Separate component for item row to avoid re-rendering issues
interface GoodsReceiptItemRowProps {
  index: number;
  form: any;
  onRemove: () => void;
  selectedPurchaseOrder?: PurchaseOrder | null;
  onSubstitutionRequest?: (itemIndex: number, originalProductId: string) => void;
}

function GoodsReceiptItemRow({ index, form, onRemove, selectedPurchaseOrder, onSubstitutionRequest }: GoodsReceiptItemRowProps) {
  const quantityReceived = form.watch(`items.${index}.quantityReceived`);
  const quantityOrdered = form.watch(`items.${index}.quantityOrdered`);
  const unitPrice = form.watch(`items.${index}.unitPrice`);
  const isSubstitution = form.watch(`items.${index}.isSubstitution`);
  const totalPrice = quantityReceived * unitPrice;

  // Show purchase order context if available
  const showPOContext = quantityOrdered && quantityOrdered > 0;

  // Determine if product should be locked (from PO)
  const isProductFromPO = selectedPurchaseOrder && showPOContext;
  const [isProductLocked, setIsProductLocked] = useState(isProductFromPO && !isSubstitution);

  // Update lock state when substitution status changes
  useEffect(() => {
    if (isSubstitution) {
      setIsProductLocked(false);
    } else if (isProductFromPO) {
      setIsProductLocked(true);
    }
  }, [isSubstitution, isProductFromPO]);

  // Handle product substitution
  const handleProductSubstitution = useCallback(() => {
    const currentProductId = form.getValues(`items.${index}.productId`);
    if (onSubstitutionRequest) {
      onSubstitutionRequest(index, currentProductId);
    } else {
      // Fallback to simple substitution
      setIsProductLocked(false);
      form.setValue(`items.${index}.isSubstitution`, true);
      form.setValue(`items.${index}.substitutionReason`, 'supplier_substitution');
      toast.info('Produk dapat diubah. Pastikan untuk mendokumentasikan alasan substitusi.');
    }
  }, [form, index, onSubstitutionRequest]);

  return (
    <TableRow>
      <TableCell className="min-w-[200px]">
        <FormField
          control={form.control}
          name={`items.${index}.productId`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="space-y-1">
                  <ProductSelector
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Pilih produk..."
                    disabled={isProductLocked}
                  />
                  {isProductFromPO && (
                    <div className="flex items-center justify-between">
                      {isProductLocked ? (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-blue-600">
                            🔒 Produk dari PO
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={handleProductSubstitution}
                            className="h-5 px-2 text-xs text-orange-600 hover:text-orange-800"
                          >
                            Substitusi
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-orange-600">
                              ⚠️ Produk disubstitusi
                            </span>
                            <span className="text-xs text-green-600">
                              ✓ Disetujui
                            </span>
                          </div>
                          <FormField
                            control={form.control}
                            name={`items.${index}.substitutionReason`}
                            render={({ field }) => (
                              <Select value={field.value || 'supplier_substitution'} onValueChange={field.onChange}>
                                <SelectTrigger className="h-6 text-xs">
                                  <SelectValue placeholder="Alasan substitusi..." />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="supplier_substitution">Substitusi Supplier</SelectItem>
                                  <SelectItem value="stock_unavailable">Stok Tidak Tersedia</SelectItem>
                                  <SelectItem value="quality_issue">Masalah Kualitas</SelectItem>
                                  <SelectItem value="expired_product">Produk Kadaluarsa</SelectItem>
                                  <SelectItem value="damaged_product">Produk Rusak</SelectItem>
                                  <SelectItem value="emergency_procurement">Pengadaan Darurat</SelectItem>
                                  <SelectItem value="other">Lainnya</SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`items.${index}.substitutionNotes`}
                            render={({ field }) => (
                              <Input
                                placeholder="Catatan substitusi..."
                                className="h-6 text-xs"
                                {...field}
                              />
                            )}
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell className="min-w-[200px]">
        <div className="space-y-2">
          <FormField
            control={form.control}
            name={`items.${index}.batchNumber`}
            render={({ field, fieldState }) => {
              const currentItem = form.watch(`items.${index}`);
              return (
                <FormItem>
                  <FormControl>
                    <AdvancedBatchNumberInput
                      value={field.value || ''}
                      onChange={field.onChange}
                      productId={currentItem?.productId}
                      supplierId={form.watch('supplierId')}
                      expiryDate={currentItem?.expiryDate ? new Date(currentItem.expiryDate) : undefined}
                      manufacturingDate={currentItem?.manufacturingDate ? new Date(currentItem.manufacturingDate) : undefined}
                      placeholder="Masukkan nomor batch..."
                      error={fieldState.error?.message}
                      className="h-8"
                      showHistory={true}
                      showFormatGuide={true}
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
          <div className="grid grid-cols-2 gap-1">
            <FormField
              control={form.control}
              name={`items.${index}.manufacturingDate`}
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input type="date" {...field} className="h-8 text-xs" title="Tanggal Produksi" />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name={`items.${index}.expiryDate`}
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input type="date" {...field} className="h-8 text-xs" title="Tanggal Kadaluarsa" />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </TableCell>
      <TableCell>
        <FormField
          control={form.control}
          name={`items.${index}.quantityReceived`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="space-y-1">
                  <Input
                    type="number"
                    min="0"
                    step="1"
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                    placeholder={showPOContext ? `Max: ${quantityOrdered}` : "0"}
                  />
                  {showPOContext && (
                    <div className="text-xs text-muted-foreground">
                      Dipesan: {quantityOrdered}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell className="min-w-[180px]">
        <div className="space-y-2">
          <FormField
            control={form.control}
            name={`items.${index}.storageLocation`}
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input placeholder="Lokasi penyimpanan..." {...field} className="h-8" />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name={`items.${index}.conditionOnReceipt`}
            render={({ field }) => (
              <FormItem>
                <Select value={field.value || 'good'} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Kondisi..." />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="good">Baik</SelectItem>
                    <SelectItem value="damaged">Rusak</SelectItem>
                    <SelectItem value="expired">Kadaluarsa</SelectItem>
                    <SelectItem value="defective">Cacat</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
        </div>
      </TableCell>
      <TableCell>
        <FormField
          control={form.control}
          name={`items.${index}.unitPrice`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell>
        <div className="font-medium">
          {formatCurrency(totalPrice)}
        </div>
      </TableCell>
      <TableCell>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
}
