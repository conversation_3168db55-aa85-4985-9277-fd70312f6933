'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  FileText,
  Download,
  Calendar,
  TrendingUp,
  TrendingDown,
  Clock
} from 'lucide-react';

interface ComplianceData {
  overallCompliance: number;
  bpomCompliant: number;
  controlledSubstances: number;
  pendingReview: number;
  nonCompliant: number;
  recentAudits: number;
  nextAuditDue: string;
  complianceCategories: {
    name: string;
    compliant: number;
    total: number;
    level: 'high' | 'medium' | 'low';
  }[];
  recentIssues: {
    batchNumber: string;
    issue: string;
    severity: 'high' | 'medium' | 'low';
    date: string;
  }[];
}

interface ComplianceReportCardProps {
  detailed?: boolean;
  className?: string;
}

export function ComplianceReportCard({ detailed = false, className }: ComplianceReportCardProps) {
  // Mock data - in real implementation, this would come from API
  const complianceData: ComplianceData = {
    overallCompliance: 92.8,
    bpomCompliant: 1156,
    controlledSubstances: 89,
    pendingReview: 23,
    nonCompliant: 68,
    recentAudits: 12,
    nextAuditDue: '2024-07-15',
    complianceCategories: [
      { name: 'Obat Bebas', compliant: 456, total: 478, level: 'high' },
      { name: 'Obat Bebas Terbatas', compliant: 234, total: 245, level: 'high' },
      { name: 'Obat Keras', compliant: 345, total: 367, level: 'medium' },
      { name: 'Obat Psikotropika', compliant: 78, total: 89, level: 'medium' },
      { name: 'Obat Narkotika', compliant: 43, total: 68, level: 'low' },
    ],
    recentIssues: [
      { batchNumber: 'BTH-2024-001', issue: 'Format tidak sesuai standar BPOM', severity: 'high', date: '2024-06-15' },
      { batchNumber: 'BTH-2024-002', issue: 'Dokumentasi tidak lengkap', severity: 'medium', date: '2024-06-14' },
      { batchNumber: 'BTH-2024-003', issue: 'Tanggal kedaluwarsa tidak valid', severity: 'low', date: '2024-06-13' },
    ],
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-blue-600';
      default: return 'text-muted-foreground';
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'default';
      default: return 'secondary';
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-red-600';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            Laporan Compliance BPOM
          </CardTitle>
          {detailed && (
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Compliance */}
        <div className="text-center space-y-2">
          <div className="text-3xl font-bold text-blue-600">
            {complianceData.overallCompliance.toFixed(1)}%
          </div>
          <div className="text-sm text-muted-foreground">Overall Compliance Rate</div>
          <Progress value={complianceData.overallCompliance} className="h-3" />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center space-y-1">
            <div className="text-xl font-bold text-green-600">
              {complianceData.bpomCompliant.toLocaleString('id-ID')}
            </div>
            <div className="text-xs text-muted-foreground">BPOM Compliant</div>
          </div>
          <div className="text-center space-y-1">
            <div className="text-xl font-bold text-yellow-600">
              {complianceData.pendingReview}
            </div>
            <div className="text-xs text-muted-foreground">Pending Review</div>
          </div>
        </div>

        {detailed && (
          <>
            <Separator />
            
            {/* Compliance by Category */}
            <div className="space-y-4">
              <h4 className="font-medium text-sm">Compliance per Kategori</h4>
              {complianceData.complianceCategories.map((category, index) => {
                const percentage = (category.compliant / category.total) * 100;
                return (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{category.name}</span>
                      <div className="flex items-center gap-2">
                        <span className={`text-sm font-medium ${getLevelColor(category.level)}`}>
                          {percentage.toFixed(1)}%
                        </span>
                        <span className="text-xs text-muted-foreground">
                          ({category.compliant}/{category.total})
                        </span>
                      </div>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                );
              })}
            </div>

            <Separator />

            {/* Recent Issues */}
            <div className="space-y-4">
              <h4 className="font-medium text-sm">Isu Compliance Terbaru</h4>
              {complianceData.recentIssues.map((issue, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  <AlertTriangle className={`h-4 w-4 mt-0.5 ${getSeverityColor(issue.severity)}`} />
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm">{issue.batchNumber}</span>
                      <Badge variant={getSeverityBadge(issue.severity) as any} className="text-xs">
                        {issue.severity}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{issue.issue}</p>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {new Date(issue.date).toLocaleDateString('id-ID')}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <Separator />

            {/* Audit Information */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Informasi Audit</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span>Audit Terbaru: {complianceData.recentAudits}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Audit Berikutnya: {new Date(complianceData.nextAuditDue).toLocaleDateString('id-ID')}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Summary for non-detailed view */}
        {!detailed && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span>Non-Compliant</span>
              <span className="font-medium text-red-600">{complianceData.nonCompliant}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span>Controlled Substances</span>
              <span className="font-medium">{complianceData.controlledSubstances}</span>
            </div>
            <div className="text-xs text-muted-foreground text-center">
              Audit berikutnya: {new Date(complianceData.nextAuditDue).toLocaleDateString('id-ID')}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
