'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Shield,
  FileCheck,
  Zap,
  Clock,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface ValidationStatsData {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  warningValidations: number;
  formatValidationRate: number;
  uniquenessValidationRate: number;
  bpomComplianceRate: number;
  dateAlignmentRate: number;
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  };
}

interface BatchValidationStatsProps {
  detailed?: boolean;
  className?: string;
}

export function BatchValidationStats({ detailed = false, className }: BatchValidationStatsProps) {
  // Mock data - in real implementation, this would come from API
  const stats: ValidationStatsData = {
    totalValidations: 1247,
    successfulValidations: 1156,
    failedValidations: 68,
    warningValidations: 23,
    formatValidationRate: 94.2,
    uniquenessValidationRate: 98.7,
    bpomComplianceRate: 92.8,
    dateAlignmentRate: 96.1,
    trend: {
      direction: 'up',
      percentage: 2.3,
    },
  };

  const successRate = (stats.successfulValidations / stats.totalValidations) * 100;
  const failureRate = (stats.failedValidations / stats.totalValidations) * 100;
  const warningRate = (stats.warningValidations / stats.totalValidations) * 100;

  const validationRules = [
    {
      name: 'Format Validation',
      rate: stats.formatValidationRate,
      icon: FileCheck,
      description: 'Validasi format batch number sesuai standar',
    },
    {
      name: 'Uniqueness Check',
      rate: stats.uniquenessValidationRate,
      icon: Zap,
      description: 'Pengecekan keunikan batch number',
    },
    {
      name: 'BPOM Compliance',
      rate: stats.bpomComplianceRate,
      icon: Shield,
      description: 'Compliance terhadap regulasi BPOM',
    },
    {
      name: 'Date Alignment',
      rate: stats.dateAlignmentRate,
      icon: Clock,
      description: 'Kesesuaian tanggal produksi dan kedaluwarsa',
    },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Statistik Validasi Batch</CardTitle>
          <div className="flex items-center gap-1">
            {stats.trend.direction === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : stats.trend.direction === 'down' ? (
              <TrendingDown className="h-4 w-4 text-red-500" />
            ) : null}
            <span className={`text-sm font-medium ${
              stats.trend.direction === 'up' ? 'text-green-600' : 
              stats.trend.direction === 'down' ? 'text-red-600' : 
              'text-muted-foreground'
            }`}>
              {stats.trend.direction === 'up' ? '+' : stats.trend.direction === 'down' ? '-' : ''}
              {stats.trend.percentage}%
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-2">
            <div className="flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-1" />
              <span className="text-2xl font-bold text-green-600">
                {stats.successfulValidations.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">Berhasil</div>
            <div className="text-sm font-medium text-green-600">
              {successRate.toFixed(1)}%
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mr-1" />
              <span className="text-2xl font-bold text-yellow-600">
                {stats.warningValidations.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">Warning</div>
            <div className="text-sm font-medium text-yellow-600">
              {warningRate.toFixed(1)}%
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center">
              <XCircle className="h-5 w-5 text-red-500 mr-1" />
              <span className="text-2xl font-bold text-red-600">
                {stats.failedValidations.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">Gagal</div>
            <div className="text-sm font-medium text-red-600">
              {failureRate.toFixed(1)}%
            </div>
          </div>
        </div>

        {/* Validation Rules Performance */}
        {detailed && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm">Performa Aturan Validasi</h4>
            {validationRules.map((rule, index) => {
              const IconComponent = rule.icon;
              return (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{rule.name}</span>
                    </div>
                    <Badge variant={rule.rate >= 95 ? 'default' : rule.rate >= 90 ? 'secondary' : 'destructive'}>
                      {rule.rate.toFixed(1)}%
                    </Badge>
                  </div>
                  <Progress value={rule.rate} className="h-2" />
                  {detailed && (
                    <p className="text-xs text-muted-foreground">{rule.description}</p>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Quick Summary for non-detailed view */}
        {!detailed && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span>Success Rate</span>
              <span className="font-medium text-green-600">{successRate.toFixed(1)}%</span>
            </div>
            <Progress value={successRate} className="h-2" />
            
            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
              <div>Total Validasi: {stats.totalValidations.toLocaleString('id-ID')}</div>
              <div>BPOM Compliance: {stats.bpomComplianceRate.toFixed(1)}%</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
