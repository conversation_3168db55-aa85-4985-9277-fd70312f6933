'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  History,
  HelpCircle,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBatchValidation, useBatchValidationRules } from '@/hooks/use-batch-validation';
import { BatchHistoryDialog } from '@/components/batch-validation/batch-history-dialog';

interface ValidationResult {
  isValid: boolean;
  message?: string;
  level: 'error' | 'warning' | 'success';
}

interface BatchValidationProps {
  value: string;
  onChange: (value: string) => void;
  productId?: string;
  supplierId?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  showHistory?: boolean;
  showFormatGuide?: boolean;
  className?: string;
  error?: string;
}

interface FormatExample {
  pattern: string;
  description: string;
  example: string;
}

const VALIDATION_PATTERNS: Record<string, FormatExample> = {
  STANDARD: {
    pattern: '^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$',
    description: 'Format standar farmasi (2-4 huruf + 4-8 angka + opsional alphanumeric)',
    example: 'ABC12345678'
  },
  BPOM_COMPLIANT: {
    pattern: '^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$',
    description: 'Format sesuai BPOM dengan kode tanggal (2-4 huruf + 6 angka tanggal + 2-6 alphanumeric)',
    example: 'KMF240615AB'
  },
  CONTROLLED: {
    pattern: '^[A-Z]{3}[0-9]{8}[A-Z]{2}$',
    description: 'Format khusus obat terkontrol (3 huruf + 8 angka + 2 huruf)',
    example: 'NAR12345678AB'
  },
  BASIC: {
    pattern: '^[A-Za-z0-9\\-_]{3,20}$',
    description: 'Format dasar (3-20 karakter: huruf, angka, -, _)',
    example: 'BATCH-123'
  }
};

export function AdvancedBatchNumberInput({
  value,
  onChange,
  productId,
  supplierId,
  expiryDate,
  manufacturingDate,
  label = 'Nomor Batch',
  placeholder = 'Masukkan nomor batch...',
  disabled = false,
  required = false,
  showHistory = true,
  showFormatGuide = true,
  className,
  error
}: BatchValidationProps) {
  const [showFormatDialog, setShowFormatDialog] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  // Use the batch validation hook
  const {
    validationResult,
    isLoading: isValidating
  } = useBatchValidation(value, {
    productId,
    supplierId,
    expiryDate,
    manufacturingDate
  });

  // Get validation rules for format guide
  const { data: validationRules } = useBatchValidationRules();



  // Get validation icon and color
  const getValidationIcon = () => {
    if (isValidating) {
      return <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />;
    }

    if (!validationResult) return null;

    switch (validationResult.level) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getValidationColor = () => {
    if (!validationResult) return '';

    switch (validationResult.level) {
      case 'success':
        return 'border-green-500 focus:border-green-500';
      case 'warning':
        return 'border-yellow-500 focus:border-yellow-500';
      case 'error':
        return 'border-red-500 focus:border-red-500';
      default:
        return '';
    }
  };

  const handleShowHistory = async () => {
    if (!value) return;
    setShowHistoryDialog(true);
    // History dialog content will be loaded when opened
  };

  return (
    <div className={cn('space-y-2', className)}>
      {/* Label with help icons */}
      <div className="flex items-center gap-2">
        <Label htmlFor="batch-number" className={required ? "after:content-['*'] after:text-red-500" : ""}>
          {label}
        </Label>

        {showFormatGuide && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => setShowFormatDialog(true)}
              >
                <HelpCircle className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Lihat panduan format batch number</p>
            </TooltipContent>
          </Tooltip>
        )}

        {showHistory && value && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={handleShowHistory}
              >
                <History className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Lihat riwayat batch number</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>

      {/* Input with validation indicator */}
      <div className="relative">
        <Input
          id="batch-number"
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'pr-10',
            getValidationColor(),
            error && 'border-red-500 focus:border-red-500'
          )}
        />

        {/* Validation icon */}
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          {getValidationIcon()}
        </div>
      </div>

      {/* Validation message */}
      {validationResult?.message && (
        <div className={cn(
          'flex items-center gap-2 text-sm',
          validationResult.level === 'success' && 'text-green-600',
          validationResult.level === 'warning' && 'text-yellow-600',
          validationResult.level === 'error' && 'text-red-600'
        )}>
          {getValidationIcon()}
          <span>{validationResult.message}</span>
        </div>
      )}

      {/* External error message */}
      {error && (
        <div className="flex items-center gap-2 text-sm text-red-600">
          <XCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      {/* Format Guide Dialog */}
      <Dialog open={showFormatDialog} onOpenChange={setShowFormatDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Panduan Format Nomor Batch</DialogTitle>
            <DialogDescription>
              Pilih format yang sesuai dengan jenis produk dan standar yang berlaku
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="max-h-96">
            <div className="space-y-4">
              {validationRules?.formats ? (
                Object.entries(validationRules.formats).map(([key, pattern]: [string, any]) => (
                  <div key={key} className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline">{key}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {pattern.description}
                    </p>
                    <div className="bg-muted p-2 rounded text-sm font-mono">
                      Contoh: {pattern.example}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Memuat aturan validasi...</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Batch History Dialog */}
      <BatchHistoryDialog
        batchNumber={value}
        open={showHistoryDialog}
        onOpenChange={setShowHistoryDialog}
      />
    </div>
  );
}
