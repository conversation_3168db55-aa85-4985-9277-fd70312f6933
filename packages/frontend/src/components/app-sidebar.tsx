"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import {
  Home,
  Pill,
  Package,
  ShoppingCart,
  Users,
  Building2,
  DollarSign,
  Settings,
  CreditCard,
  FileText,
  Truck,
  ChevronDown,
  ChevronRight,
  ClipboardCheck,
  Shield,
  Search,
  History,
  BarChart3,
  AlertTriangle,
  FileBarChart,
  Database,
  UserCheck,
  Stethoscope,
  FlaskConical,
  CheckCircle2,
  Calendar,
  TrendingUp,
  Archive,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
} from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { NavUser } from "@/components/nav-user"

interface NavigationItem {
  name: string
  href?: string
  icon: React.ComponentType<{ className?: string }>
  children?: NavigationItem[]
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },

  // Procurement Management - Comprehensive dropdown
  {
    name: 'Manajemen Pengadaan',
    icon: Truck,
    children: [
      { name: 'Purchase Orders', href: '/dashboard/purchase-orders', icon: FileText },
      { name: 'Buat Purchase Order', href: '/dashboard/purchase-orders/create', icon: FileText },
      { name: 'Penerimaan Barang', href: '/dashboard/goods-receipts', icon: Truck },
      { name: 'Buat Penerimaan Barang', href: '/dashboard/goods-receipts/create', icon: Truck },
      { name: 'Kontrol Kualitas', href: '/dashboard/goods-receipts/quality-control', icon: ClipboardCheck },
      { name: 'Validasi Batch', href: '/dashboard/procurement/batch-validation', icon: Search },
      { name: 'Riwayat Batch', href: '/dashboard/procurement/batch-validation/history', icon: History },
      { name: 'Kepatuhan BPOM', href: '/dashboard/procurement/bpom-compliance', icon: Shield },
      { name: 'Obat Terkontrol', href: '/dashboard/procurement/bpom-compliance/controlled-substances', icon: AlertTriangle },
      { name: 'Persiapan Inspeksi', href: '/dashboard/procurement/bpom-compliance/inspection-prep', icon: CheckCircle2 },
      { name: 'Manajemen Pemasok', href: '/dashboard/suppliers', icon: Building2 },
      { name: 'Analitik Pengadaan', href: '/dashboard/procurement/analytics', icon: BarChart3 },
    ]
  },

  // Inventory Management
  {
    name: 'Manajemen Inventori',
    icon: Package,
    children: [
      { name: 'Stok Inventori', href: '/dashboard/inventory', icon: Package },
      { name: 'Manajemen Produk', href: '/dashboard/products', icon: Pill },
      { name: 'Kategori Produk', href: '/dashboard/products/categories', icon: Archive },
      { name: 'Unit Produk', href: '/dashboard/products/units', icon: Database },
      { name: 'Stok Opname', href: '/dashboard/inventory/stock-opname', icon: ClipboardCheck },
      { name: 'Perpindahan Stok', href: '/dashboard/inventory/movements', icon: TrendingUp },
      { name: 'Laporan Inventori', href: '/dashboard/inventory/reports', icon: FileBarChart },
    ]
  },

  // Sales & POS
  {
    name: 'Penjualan & Kasir',
    icon: ShoppingCart,
    children: [
      { name: 'Penjualan', href: '/dashboard/sales', icon: ShoppingCart },
      { name: 'POS Kasir', href: '/pos', icon: CreditCard },
      { name: 'Riwayat Transaksi', href: '/dashboard/sales/history', icon: History },
      { name: 'Laporan Penjualan', href: '/dashboard/sales/reports', icon: FileBarChart },
    ]
  },

  // Reports & Analytics
  {
    name: 'Laporan & Analitik',
    icon: BarChart3,
    children: [
      { name: 'Dashboard Analitik', href: '/dashboard/analytics', icon: BarChart3 },
      { name: 'Laporan Keuangan', href: '/dashboard/finance/reports', icon: DollarSign },
      { name: 'Laporan Pengadaan', href: '/dashboard/procurement/reports', icon: FileText },
      { name: 'Laporan Inventori', href: '/dashboard/inventory/reports', icon: Package },
      { name: 'Laporan Penjualan', href: '/dashboard/sales/reports', icon: ShoppingCart },
      { name: 'Laporan BPOM', href: '/dashboard/procurement/bpom-compliance/reports', icon: Shield },
      { name: 'Audit Trail', href: '/dashboard/audit-trail', icon: History },
    ]
  },

  // Customer & Supplier Management
  {
    name: 'Manajemen Relasi',
    icon: Users,
    children: [
      { name: 'Pelanggan', href: '/dashboard/customers', icon: Users },
      { name: 'Pemasok', href: '/dashboard/suppliers', icon: Building2 },
      { name: 'Dokter', href: '/dashboard/doctors', icon: Stethoscope },
      { name: 'Apoteker', href: '/dashboard/pharmacists', icon: UserCheck },
    ]
  },

  // Financial Management
  { name: 'Keuangan', href: '/dashboard/finance', icon: DollarSign },

  // System Administration
  {
    name: 'Administrasi Sistem',
    icon: Settings,
    children: [
      { name: 'Pengaturan Umum', href: '/dashboard/settings', icon: Settings },
      { name: 'Manajemen Pengguna', href: '/dashboard/settings/users', icon: Users },
      { name: 'Pengaturan Apotek', href: '/dashboard/settings/pharmacy', icon: Pill },
      { name: 'Backup & Restore', href: '/dashboard/settings/backup', icon: Archive },
      { name: 'Log Sistem', href: '/dashboard/settings/logs', icon: History },
    ]
  },
]

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user?: any
  variant?: "sidebar" | "floating" | "inset"
}

export function AppSidebar({ user, variant = "sidebar", ...props }: AppSidebarProps) {
  const pathname = usePathname()
  const [pharmacyName, setPharmacyName] = useState('Apotek App')
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({})

  useEffect(() => {
    // Fetch pharmacy settings
    const fetchPharmacySettings = async () => {
      try {
        // This will be implemented when we have the API integration
        // For now, use default name
        setPharmacyName('Apotek Sehat Bersama')
      } catch (error) {
        console.error('Failed to fetch pharmacy settings:', error)
      }
    }

    fetchPharmacySettings()
  }, [])

  // Auto-expand menu if current path matches any child
  useEffect(() => {
    const newOpenMenus: Record<string, boolean> = {}
    navigation.forEach((item) => {
      if (item.children) {
        const hasActiveChild = item.children.some(child =>
          child.href && (pathname === child.href || pathname.startsWith(child.href + '/'))
        )
        if (hasActiveChild) {
          newOpenMenus[item.name] = true
        }
      }
    })
    setOpenMenus(prev => ({ ...prev, ...newOpenMenus }))
  }, [pathname])

  const toggleMenu = (menuName: string) => {
    setOpenMenus(prev => ({
      ...prev,
      [menuName]: !prev[menuName]
    }))
  }

  const isMenuItemActive = (item: NavigationItem): boolean => {
    if (item.href) {
      return pathname === item.href || (item.href !== '/dashboard' && pathname.startsWith(item.href))
    }
    if (item.children) {
      return item.children.some(child =>
        child.href && (pathname === child.href || pathname.startsWith(child.href + '/'))
      )
    }
    return false
  }

  return (
    <Sidebar collapsible="offcanvas" variant={variant} {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
                    <Pill className="h-5 w-5 text-primary-foreground" />
                  </div>
                </div>
                <div className="ml-3">
                  <span className="text-base font-semibold">{pharmacyName}</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.map((item) => {
                const isActive = isMenuItemActive(item)

                // If item has children, render as collapsible menu
                if (item.children) {
                  return (
                    <Collapsible
                      key={item.name}
                      open={openMenus[item.name]}
                      onOpenChange={() => toggleMenu(item.name)}
                    >
                      <SidebarMenuItem>
                        <CollapsibleTrigger asChild>
                          <SidebarMenuButton
                            tooltip={item.name}
                            isActive={isActive}
                            className="w-full justify-between"
                          >
                            <div className="flex items-center gap-2">
                              <item.icon className="h-5 w-5" />
                              <span>{item.name}</span>
                            </div>
                            {openMenus[item.name] ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </SidebarMenuButton>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <SidebarMenuSub>
                            {item.children.map((child) => {
                              const isChildActive = child.href && (
                                pathname === child.href ||
                                pathname.startsWith(child.href + '/')
                              )

                              return (
                                <SidebarMenuSubItem key={child.name}>
                                  <SidebarMenuSubButton
                                    asChild
                                    isActive={isChildActive}
                                  >
                                    <Link href={child.href || '#'}>
                                      <child.icon className="h-4 w-4" />
                                      <span>{child.name}</span>
                                    </Link>
                                  </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                              )
                            })}
                          </SidebarMenuSub>
                        </CollapsibleContent>
                      </SidebarMenuItem>
                    </Collapsible>
                  )
                }

                // Regular menu item without children
                return (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.name}
                      isActive={isActive}
                    >
                      <Link href={item.href || '#'}>
                        <item.icon className="h-5 w-5" />
                        <span>{item.name}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        {user && <NavUser user={user} />}
      </SidebarFooter>
    </Sidebar>
  )
}
