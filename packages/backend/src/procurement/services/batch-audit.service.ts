import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BatchAuditAction } from '@prisma/client';

/**
 * Context information for batch audit logging
 */
export interface BatchAuditContext {
  batchNumber: string;
  action: BatchAuditAction;
  status: 'SUCCESS' | 'FAILED' | 'WARNING';
  productId?: string;
  supplierId?: string;
  userId?: string;
  referenceType?: string;
  referenceId?: string;
  referenceNumber?: string;
  validationRules?: string[];
  validationResults?: any;
  bpomCompliant?: boolean;
  complianceNotes?: string;
  complianceLevel?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  message?: string;
  details?: string;
  errorMessage?: string;
}

/**
 * Batch audit query parameters
 */
export interface BatchAuditQuery {
  batchNumber?: string;
  productId?: string;
  supplierId?: string;
  userId?: string;
  action?: BatchAuditAction;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}

/**
 * Service for managing batch number audit trails
 * Provides comprehensive logging and tracking of all batch number operations
 */
@Injectable()
export class BatchAuditService {
  private readonly logger = new Logger(BatchAuditService.name);

  constructor(private prisma: PrismaService) {}

  /**
   * Log a batch audit event
   */
  async logAuditEvent(context: BatchAuditContext): Promise<void> {
    try {
      await this.prisma.batchAuditLog.create({
        data: {
          batchNumber: context.batchNumber,
          action: context.action,
          status: context.status,
          productId: context.productId,
          supplierId: context.supplierId,
          userId: context.userId,
          referenceType: context.referenceType,
          referenceId: context.referenceId,
          referenceNumber: context.referenceNumber,
          validationRules: context.validationRules || [],
          validationResults: context.validationResults,
          bpomCompliant: context.bpomCompliant,
          complianceNotes: context.complianceNotes,
          complianceLevel: context.complianceLevel,
          oldValues: context.oldValues,
          newValues: context.newValues,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          sessionId: context.sessionId,
          message: context.message,
          details: context.details,
          errorMessage: context.errorMessage,
        },
      });

      this.logger.log(`Batch audit logged: ${context.batchNumber} - ${context.action} - ${context.status}`);
    } catch (error) {
      this.logger.error(`Failed to log batch audit event: ${error.message}`, error.stack);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Log batch creation event
   */
  async logBatchCreated(
    batchNumber: string,
    productId: string,
    supplierId?: string,
    userId?: string,
    referenceType?: string,
    referenceId?: string,
    additionalContext?: Partial<BatchAuditContext>
  ): Promise<void> {
    await this.logAuditEvent({
      batchNumber,
      action: BatchAuditAction.CREATED,
      status: 'SUCCESS',
      productId,
      supplierId,
      userId,
      referenceType,
      referenceId,
      message: `Batch number ${batchNumber} berhasil dibuat`,
      ...additionalContext,
    });
  }

  /**
   * Log batch validation event
   */
  async logBatchValidation(
    batchNumber: string,
    productId: string,
    validationResults: any,
    isValid: boolean,
    validationRules: string[],
    userId?: string,
    additionalContext?: Partial<BatchAuditContext>
  ): Promise<void> {
    await this.logAuditEvent({
      batchNumber,
      action: BatchAuditAction.VALIDATED,
      status: isValid ? 'SUCCESS' : 'FAILED',
      productId,
      userId,
      validationRules,
      validationResults,
      message: isValid 
        ? `Batch number ${batchNumber} berhasil divalidasi`
        : `Batch number ${batchNumber} gagal validasi`,
      ...additionalContext,
    });
  }

  /**
   * Log BPOM compliance check
   */
  async logBPOMComplianceCheck(
    batchNumber: string,
    productId: string,
    isCompliant: boolean,
    complianceLevel: string,
    complianceNotes?: string,
    userId?: string,
    additionalContext?: Partial<BatchAuditContext>
  ): Promise<void> {
    await this.logAuditEvent({
      batchNumber,
      action: BatchAuditAction.COMPLIANCE_CHECK,
      status: isCompliant ? 'SUCCESS' : 'WARNING',
      productId,
      userId,
      bpomCompliant: isCompliant,
      complianceLevel,
      complianceNotes,
      message: `BPOM compliance check untuk batch ${batchNumber}: ${isCompliant ? 'Compliant' : 'Non-compliant'}`,
      ...additionalContext,
    });
  }

  /**
   * Log batch substitution event
   */
  async logBatchSubstitution(
    originalBatchNumber: string,
    newBatchNumber: string,
    productId: string,
    reason: string,
    userId?: string,
    referenceType?: string,
    referenceId?: string,
    additionalContext?: Partial<BatchAuditContext>
  ): Promise<void> {
    await this.logAuditEvent({
      batchNumber: newBatchNumber,
      action: BatchAuditAction.SUBSTITUTED,
      status: 'SUCCESS',
      productId,
      userId,
      referenceType,
      referenceId,
      oldValues: { batchNumber: originalBatchNumber },
      newValues: { batchNumber: newBatchNumber },
      details: reason,
      message: `Batch number disubstitusi dari ${originalBatchNumber} ke ${newBatchNumber}`,
      ...additionalContext,
    });
  }

  /**
   * Get audit trail for a specific batch number
   */
  async getBatchAuditTrail(batchNumber: string, limit = 50, offset = 0) {
    return this.prisma.batchAuditLog.findMany({
      where: { batchNumber },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Query audit logs with filters
   */
  async queryAuditLogs(query: BatchAuditQuery) {
    const where: any = {};

    if (query.batchNumber) {
      where.batchNumber = { contains: query.batchNumber, mode: 'insensitive' };
    }
    if (query.productId) {
      where.productId = query.productId;
    }
    if (query.supplierId) {
      where.supplierId = query.supplierId;
    }
    if (query.userId) {
      where.userId = query.userId;
    }
    if (query.action) {
      where.action = query.action;
    }
    if (query.status) {
      where.status = query.status;
    }
    if (query.dateFrom || query.dateTo) {
      where.createdAt = {};
      if (query.dateFrom) {
        where.createdAt.gte = query.dateFrom;
      }
      if (query.dateTo) {
        where.createdAt.lte = query.dateTo;
      }
    }

    const [logs, total] = await Promise.all([
      this.prisma.batchAuditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          supplier: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: query.limit || 50,
        skip: query.offset || 0,
      }),
      this.prisma.batchAuditLog.count({ where }),
    ]);

    return {
      logs,
      total,
      hasMore: (query.offset || 0) + logs.length < total,
    };
  }

  /**
   * Get audit statistics for a batch number
   */
  async getBatchAuditStats(batchNumber: string) {
    const stats = await this.prisma.batchAuditLog.groupBy({
      by: ['action', 'status'],
      where: { batchNumber },
      _count: true,
    });

    const summary = {
      totalEvents: 0,
      successfulEvents: 0,
      failedEvents: 0,
      warningEvents: 0,
      actionBreakdown: {} as Record<string, number>,
    };

    stats.forEach((stat) => {
      summary.totalEvents += stat._count;
      
      if (stat.status === 'SUCCESS') {
        summary.successfulEvents += stat._count;
      } else if (stat.status === 'FAILED') {
        summary.failedEvents += stat._count;
      } else if (stat.status === 'WARNING') {
        summary.warningEvents += stat._count;
      }

      summary.actionBreakdown[stat.action] = (summary.actionBreakdown[stat.action] || 0) + stat._count;
    });

    return summary;
  }
}
