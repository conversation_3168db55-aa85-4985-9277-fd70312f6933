import { Test, TestingModule } from '@nestjs/testing';
import { BatchNumberValidationService } from '../batch-number-validation.service';
import { BatchAuditService } from '../batch-audit.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ProductType, MedicineClassification } from '@prisma/client';

describe('BatchNumberValidationService', () => {
  let service: BatchNumberValidationService;
  let prismaService: PrismaService;
  let batchAuditService: BatchAuditService;

  const mockPrismaService = {
    product: {
      findUnique: jest.fn(),
    },
    inventoryItem: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    goodsReceiptItem: {
      findMany: jest.fn(),
    },
    supplier: {
      findUnique: jest.fn(),
    },
  };

  const mockBatchAuditService = {
    logBatchValidation: jest.fn(),
    logAuditEvent: jest.fn(),
    logBatchSubstitution: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BatchNumberValidationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: BatchAuditService,
          useValue: mockBatchAuditService,
        },
      ],
    }).compile();

    service = module.get<BatchNumberValidationService>(BatchNumberValidationService);
    prismaService = module.get<PrismaService>(PrismaService);
    batchAuditService = module.get<BatchAuditService>(BatchAuditService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateBatchNumber', () => {
    const mockProduct = {
      id: 'product-1',
      name: 'Test Medicine',
      type: ProductType.MEDICINE,
      medicineClassification: MedicineClassification.OBAT_KERAS,
      bpomNumber: 'DKL1234567890A1',
      manufacturer: 'PT Test Pharma',
    };

    beforeEach(() => {
      mockPrismaService.product.findUnique.mockResolvedValue(mockProduct);
      mockPrismaService.inventoryItem.findMany.mockResolvedValue([]);
      mockPrismaService.goodsReceiptItem.findMany.mockResolvedValue([]);
    });

    it('should validate a valid BPOM compliant batch number', async () => {
      const context = {
        batchNumber: 'ABC240615XY',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(true);
      expect(result.bpomCompliant).toBe(true);
      expect(result.validationLevel).toBe('BPOM_COMPLIANT');
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid batch number format', async () => {
      const context = {
        batchNumber: 'invalid@batch#',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(error => error.includes('karakter'))).toBe(true);
    });

    it('should reject batch number that is too short', async () => {
      const context = {
        batchNumber: 'AB',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('minimal 3 karakter'))).toBe(true);
    });

    it('should reject batch number that is too long', async () => {
      const context = {
        batchNumber: 'A'.repeat(25),
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('maksimal 20 karakter'))).toBe(true);
    });

    it('should detect duplicate batch numbers', async () => {
      mockPrismaService.inventoryItem.findMany.mockResolvedValue([
        {
          id: 'existing-1',
          batchNumber: 'ABC240615XY',
          product: { name: 'Test Medicine' },
          supplier: { name: 'Test Supplier' },
          createdAt: new Date(),
        },
      ]);

      const context = {
        batchNumber: 'ABC240615XY',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.uniquenessCheck.isUnique).toBe(false);
      expect(result.errors.some(error => error.includes('sudah digunakan'))).toBe(true);
    });

    it('should validate controlled substance batch format', async () => {
      const narcoticProduct = {
        ...mockProduct,
        medicineClassification: MedicineClassification.NARKOTIKA,
      };
      mockPrismaService.product.findUnique.mockResolvedValue(narcoticProduct);

      const context = {
        batchNumber: 'NAR12345678AB',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.validationLevel).toBe('CONTROLLED');
      expect(result.bpomCompliant).toBe(true);
    });

    it('should reject invalid controlled substance batch format', async () => {
      const narcoticProduct = {
        ...mockProduct,
        medicineClassification: MedicineClassification.NARKOTIKA,
      };
      mockPrismaService.product.findUnique.mockResolvedValue(narcoticProduct);

      const context = {
        batchNumber: 'INVALID123',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Narkotika'))).toBe(true);
    });

    it('should handle date validation in batch numbers', async () => {
      const context = {
        batchNumber: 'ABC241301XY', // Invalid month (13)
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
        expiryDate: new Date('2025-06-15'),
        manufacturingDate: new Date('2024-06-15'),
      };

      const result = await service.validateBatchNumber(context);

      expect(result.warnings.some(warning => warning.includes('Bulan'))).toBe(true);
    });

    it('should validate expiry date alignment', async () => {
      const pastDate = new Date();
      pastDate.setFullYear(pastDate.getFullYear() - 1);

      const context = {
        batchNumber: 'ABC240615XY',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
        expiryDate: pastDate, // Past expiry date
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('masa lalu'))).toBe(true);
    });

    it('should handle edge case: empty batch number', async () => {
      const context = {
        batchNumber: '',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('tidak boleh kosong'))).toBe(true);
    });

    it('should handle edge case: whitespace in batch number', async () => {
      const context = {
        batchNumber: ' ABC240615XY ',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      const result = await service.validateBatchNumber(context);

      expect(result.warnings.some(warning => warning.includes('spasi'))).toBe(true);
    });

    it('should handle substitution scenarios', async () => {
      const context = {
        batchNumber: 'ABC240615XY',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
        isSubstitution: true,
        originalBatchNumber: 'ABC240615XZ',
      };

      const result = await service.validateBatchNumber(context);

      expect(mockBatchAuditService.logBatchSubstitution).toHaveBeenCalled();
    });

    it('should log audit trail for validation', async () => {
      const context = {
        batchNumber: 'ABC240615XY',
        productId: 'product-1',
        supplierId: 'supplier-1',
        userId: 'user-1',
      };

      await service.validateBatchNumber(context);

      expect(mockBatchAuditService.logBatchValidation).toHaveBeenCalledWith(
        'ABC240615XY',
        'product-1',
        expect.any(Object),
        expect.any(Boolean),
        expect.any(Array),
        'user-1',
        expect.any(Object)
      );
    });
  });

  describe('validateRealTime', () => {
    beforeEach(() => {
      mockPrismaService.inventoryItem.findFirst.mockResolvedValue(null);
    });

    it('should perform quick validation for real-time feedback', async () => {
      const result = await service.validateRealTime('ABC240615XY', 'product-1');

      expect(result.isValid).toBe(true);
      expect(result.level).toBe('success');
      expect(result.message).toContain('BPOM');
    });

    it('should reject empty batch number in real-time', async () => {
      const result = await service.validateRealTime('', 'product-1');

      expect(result.isValid).toBe(false);
      expect(result.level).toBe('error');
      expect(result.message).toContain('kosong');
    });

    it('should detect duplicate in real-time', async () => {
      mockPrismaService.inventoryItem.findFirst.mockResolvedValue({
        id: 'existing-1',
        batchNumber: 'ABC240615XY',
      });

      const result = await service.validateRealTime('ABC240615XY', 'product-1');

      expect(result.isValid).toBe(false);
      expect(result.level).toBe('error');
      expect(result.message).toContain('sudah digunakan');
    });
  });

  describe('checkUniqueness', () => {
    it('should return unique status when no conflicts exist', async () => {
      mockPrismaService.inventoryItem.findMany.mockResolvedValue([]);
      mockPrismaService.goodsReceiptItem.findMany.mockResolvedValue([]);

      const result = await service.checkUniqueness('ABC240615XY', 'product-1');

      expect(result.isUnique).toBe(true);
      expect(result.conflicts).toHaveLength(0);
    });

    it('should return conflicts when duplicates exist', async () => {
      mockPrismaService.inventoryItem.findMany.mockResolvedValue([
        {
          product: { name: 'Test Medicine' },
          supplier: { name: 'Test Supplier' },
          createdAt: new Date(),
        },
      ]);
      mockPrismaService.goodsReceiptItem.findMany.mockResolvedValue([]);

      const result = await service.checkUniqueness('ABC240615XY', 'product-1');

      expect(result.isUnique).toBe(false);
      expect(result.conflicts).toHaveLength(1);
      expect(result.conflicts[0].source).toBe('inventory');
    });
  });
});
