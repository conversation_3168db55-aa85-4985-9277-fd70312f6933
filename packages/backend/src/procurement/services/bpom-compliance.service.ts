import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BatchAuditService } from './batch-audit.service';
import { MedicineClassification, ProductType, BatchAuditAction } from '@prisma/client';

/**
 * BPOM registration types and their validation rules
 */
export enum BPOMRegistrationType {
  DKL = 'DKL', // Obat keras (prescription drugs)
  DTL = 'DTL', // Obat bebas terbatas (limited OTC)
  DBL = 'DBL', // Obat bebas (OTC)
  AKL = 'AKL', // Alat kesehatan (medical devices)
  AKD = 'AKD', // Alat kesehatan diagnostik
  PKD = 'PKD', // Perbekalan kesehatan rumah tangga
}

/**
 * BPOM compliance levels
 */
export enum BPOMComplianceLevel {
  BASIC = 'BASIC',           // Basic format compliance
  STANDARD = 'STANDARD',     // Standard pharmaceutical compliance
  BPOM_REGISTERED = 'BPOM_REGISTERED', // BPOM registered product
  CONTROLLED = 'CONTROLLED', // Controlled substances (Narkotika/Psikotropika)
  IMPORT = 'IMPORT',         // Imported products
}

/**
 * BPOM compliance check result
 */
export interface BPOMComplianceResult {
  isCompliant: boolean;
  complianceLevel: BPOMComplianceLevel;
  registrationValid: boolean;
  registrationType?: BPOMRegistrationType;
  registrationNumber?: string;
  registrationExpiry?: Date;
  controlledSubstance: boolean;
  importProduct: boolean;
  warnings: string[];
  errors: string[];
  recommendations: string[];
  auditTrail: {
    checkDate: Date;
    checkType: string;
    result: string;
  }[];
}

/**
 * Controlled substance tracking data
 */
export interface ControlledSubstanceData {
  classification: MedicineClassification;
  scheduleNumber?: string;
  specialRequirements: string[];
  reportingRequired: boolean;
  storageRequirements: string[];
  dispensingLimits?: {
    maxQuantity: number;
    timeframe: string;
  };
}

/**
 * BPOM inspection preparation data
 */
export interface BPOMInspectionData {
  batchNumbers: string[];
  complianceStatus: 'READY' | 'NEEDS_ATTENTION' | 'NON_COMPLIANT';
  missingDocuments: string[];
  complianceIssues: string[];
  lastInspectionDate?: Date;
  nextInspectionDue?: Date;
  preparationChecklist: {
    item: string;
    status: 'COMPLETE' | 'PENDING' | 'MISSING';
    notes?: string;
  }[];
}

/**
 * Service for BPOM compliance management
 * Handles Indonesian pharmaceutical regulatory compliance
 */
@Injectable()
export class BPOMComplianceService {
  private readonly logger = new Logger(BPOMComplianceService.name);

  constructor(
    private prisma: PrismaService,
    private batchAuditService: BatchAuditService
  ) { }

  /**
   * Perform comprehensive BPOM compliance check
   */
  async performComplianceCheck(
    productId: string,
    batchNumber: string,
    userId?: string
  ): Promise<BPOMComplianceResult> {
    const result: BPOMComplianceResult = {
      isCompliant: false,
      complianceLevel: BPOMComplianceLevel.BASIC,
      registrationValid: false,
      controlledSubstance: false,
      importProduct: false,
      warnings: [],
      errors: [],
      recommendations: [],
      auditTrail: [],
    };

    try {
      // Get product information
      const product = await this.prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        result.errors.push('Produk tidak ditemukan');
        return result;
      }

      // Check BPOM registration
      await this.checkBPOMRegistration(product, result);

      // Check controlled substance requirements
      await this.checkControlledSubstance(product, batchNumber, result);

      // Check import product requirements (need supplier info)
      const supplier = await this.prisma.supplier.findFirst({
        where: {
          inventoryItems: {
            some: { productId }
          }
        }
      });
      await this.checkImportRequirements(product, result, supplier);

      // Validate batch number format for BPOM compliance
      await this.validateBPOMBatchFormat(batchNumber, product, result);

      // Check expiry and manufacturing date compliance
      await this.checkDateCompliance(productId, batchNumber, result);

      // Determine overall compliance level
      this.determineComplianceLevel(result);

      // Log compliance check
      await this.logComplianceCheck(productId, batchNumber, result, userId);

      result.isCompliant = result.errors.length === 0;

    } catch (error) {
      this.logger.error(`BPOM compliance check failed: ${error.message}`, error.stack);
      result.errors.push(`Pemeriksaan compliance gagal: ${error.message}`);
    }

    return result;
  }

  /**
   * Check BPOM registration validity
   */
  private async checkBPOMRegistration(product: any, result: BPOMComplianceResult): Promise<void> {
    if (!product.bpomNumber) {
      if (product.type === ProductType.MEDICINE || product.type === ProductType.MEDICAL_DEVICE) {
        result.errors.push('Produk wajib memiliki nomor registrasi BPOM');
      } else {
        result.warnings.push('Produk sebaiknya memiliki nomor registrasi BPOM');
      }
      return;
    }

    // Validate BPOM number format
    const registrationType = this.getBPOMRegistrationType(product.bpomNumber);
    if (!registrationType) {
      result.errors.push('Format nomor registrasi BPOM tidak valid');
      return;
    }

    result.registrationType = registrationType;
    result.registrationNumber = product.bpomNumber;
    result.registrationValid = true;

    // Check if registration matches product type
    const isValidForProductType = this.validateRegistrationForProductType(
      registrationType,
      product.type,
      product.medicineClassification
    );

    if (!isValidForProductType) {
      result.warnings.push(
        `Tipe registrasi BPOM (${registrationType}) mungkin tidak sesuai dengan jenis produk`
      );
    }

    // Note: BPOM expiry date tracking would need to be added to Product model
    // For now, we'll add a recommendation to track this manually
    result.recommendations.push('Pastikan registrasi BPOM masih berlaku dan tidak akan kedaluwarsa');
  }

  /**
   * Check controlled substance requirements
   */
  private async checkControlledSubstance(
    product: any,
    batchNumber: string,
    result: BPOMComplianceResult
  ): Promise<void> {
    const isNarcotic = product.medicineClassification === MedicineClassification.NARKOTIKA;
    const isPsychotropic = product.medicineClassification === MedicineClassification.PSIKOTROPIKA;

    if (isNarcotic || isPsychotropic) {
      result.controlledSubstance = true;
      result.complianceLevel = BPOMComplianceLevel.CONTROLLED;

      // Validate controlled substance batch format
      const controlledBatchRegex = /^[A-Z]{3}[0-9]{8}[A-Z]{2}$/;
      if (!controlledBatchRegex.test(batchNumber)) {
        result.errors.push(
          `Batch number obat ${isNarcotic ? 'narkotika' : 'psikotropika'} harus menggunakan format khusus (3 huruf + 8 angka + 2 huruf)`
        );
      }

      // Add controlled substance specific requirements
      result.recommendations.push(
        'Pastikan penyimpanan sesuai dengan persyaratan obat terkontrol',
        'Dokumentasi lengkap diperlukan untuk audit BPOM',
        'Pelaporan berkala ke BPOM wajib dilakukan'
      );

      if (isNarcotic) {
        result.recommendations.push(
          'Penyimpanan dalam lemari khusus narkotika',
          'Pencatatan dalam buku register narkotika'
        );
      }
    }
  }

  /**
   * Check import product requirements
   */
  private async checkImportRequirements(product: any, result: BPOMComplianceResult, supplier?: any): Promise<void> {
    // Check if product is imported
    const isImported = this.isImportedProduct(product, supplier);

    if (isImported) {
      result.importProduct = true;

      // Import products need special documentation
      result.recommendations.push(
        'Pastikan sertifikat analisis (CoA) tersedia',
        'Dokumen import dan bea cukai harus lengkap',
        'Sertifikat halal diperlukan jika applicable'
      );

      // Check for import-specific BPOM requirements
      if (product.bpomNumber && !product.bpomNumber.includes('IMP')) {
        result.warnings.push('Produk impor sebaiknya memiliki registrasi BPOM khusus impor');
      }
    }
  }

  /**
   * Validate BPOM batch number format
   */
  private async validateBPOMBatchFormat(
    batchNumber: string,
    product: any,
    result: BPOMComplianceResult
  ): Promise<void> {
    // BPOM compliant batch format: 3-4 letters + 6 digits (YYMMDD) + optional suffix
    const bpomBatchRegex = /^[A-Z]{2,4}[0-9]{6}[A-Z0-9]*$/;

    if (bpomBatchRegex.test(batchNumber.toUpperCase())) {
      result.recommendations.push('Format batch number sesuai dengan standar BPOM');

      // Extract and validate date from batch number
      const dateMatch = batchNumber.match(/[A-Z]{2,4}([0-9]{6})/);
      if (dateMatch) {
        const dateStr = dateMatch[1];
        const year = 2000 + parseInt(dateStr.substring(0, 2));
        const month = parseInt(dateStr.substring(2, 4));
        const day = parseInt(dateStr.substring(4, 6));

        // Validate date components
        if (month < 1 || month > 12) {
          result.warnings.push('Bulan dalam batch number tidak valid');
        }
        if (day < 1 || day > 31) {
          result.warnings.push('Tanggal dalam batch number tidak valid');
        }
        if (year < 2020 || year > 2030) {
          result.warnings.push('Tahun dalam batch number di luar rentang wajar');
        }
      }
    } else {
      result.warnings.push('Format batch number tidak mengikuti standar BPOM yang direkomendasikan');
    }
  }

  /**
   * Check date compliance for BPOM requirements
   */
  private async checkDateCompliance(
    productId: string,
    batchNumber: string,
    result: BPOMComplianceResult
  ): Promise<void> {
    // Get inventory items with this batch number
    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where: {
        productId,
        batchNumber: {
          equals: batchNumber,
          mode: 'insensitive'
        }
      },
      select: {
        expiryDate: true,
        receivedDate: true, // Use receivedDate as proxy for manufacturing date
      }
    });

    for (const item of inventoryItems) {
      if (item.expiryDate) {
        const now = new Date();
        const expiryDate = new Date(item.expiryDate);

        // Check if already expired
        if (expiryDate <= now) {
          result.errors.push('Batch sudah kedaluwarsa');
        }

        // Check shelf life (BPOM requires reasonable shelf life)
        // Use receivedDate as proxy for manufacturing date
        if (item.receivedDate) {
          const receivedDate = new Date(item.receivedDate);
          const shelfLifeMonths = (expiryDate.getTime() - receivedDate.getTime()) / (1000 * 60 * 60 * 24 * 30);

          if (shelfLifeMonths > 60) { // More than 5 years
            result.warnings.push('Masa simpan produk lebih dari 5 tahun, mohon verifikasi');
          }
          if (shelfLifeMonths < 6) { // Less than 6 months
            result.warnings.push('Masa simpan produk kurang dari 6 bulan');
          }
        }
      }
    }
  }

  /**
   * Determine overall compliance level
   */
  private determineComplianceLevel(result: BPOMComplianceResult): void {
    if (result.controlledSubstance) {
      result.complianceLevel = BPOMComplianceLevel.CONTROLLED;
    } else if (result.registrationValid && result.registrationType) {
      result.complianceLevel = BPOMComplianceLevel.BPOM_REGISTERED;
    } else if (result.importProduct) {
      result.complianceLevel = BPOMComplianceLevel.IMPORT;
    } else if (result.warnings.length === 0) {
      result.complianceLevel = BPOMComplianceLevel.STANDARD;
    } else {
      result.complianceLevel = BPOMComplianceLevel.BASIC;
    }
  }

  /**
   * Log compliance check for audit trail
   */
  private async logComplianceCheck(
    productId: string,
    batchNumber: string,
    result: BPOMComplianceResult,
    userId?: string
  ): Promise<void> {
    await this.batchAuditService.logBPOMComplianceCheck(
      batchNumber,
      productId,
      result.isCompliant,
      result.complianceLevel,
      result.errors.concat(result.warnings).join('; ') || undefined,
      userId,
      {
        bpomCompliant: result.isCompliant,
        complianceLevel: result.complianceLevel,
        validationResults: result,
      }
    );
  }

  /**
   * Get BPOM registration type from registration number
   */
  private getBPOMRegistrationType(bpomNumber: string): BPOMRegistrationType | null {
    const upperBpom = bpomNumber.toUpperCase();

    if (upperBpom.startsWith('DKL')) return BPOMRegistrationType.DKL;
    if (upperBpom.startsWith('DTL')) return BPOMRegistrationType.DTL;
    if (upperBpom.startsWith('DBL')) return BPOMRegistrationType.DBL;
    if (upperBpom.startsWith('AKL')) return BPOMRegistrationType.AKL;
    if (upperBpom.startsWith('AKD')) return BPOMRegistrationType.AKD;
    if (upperBpom.startsWith('PKD')) return BPOMRegistrationType.PKD;

    return null;
  }

  /**
   * Validate if registration type matches product type
   */
  private validateRegistrationForProductType(
    registrationType: BPOMRegistrationType,
    productType: ProductType,
    medicineClassification?: MedicineClassification
  ): boolean {
    switch (registrationType) {
      case BPOMRegistrationType.DKL:
        return productType === ProductType.MEDICINE &&
          medicineClassification === MedicineClassification.OBAT_KERAS;
      case BPOMRegistrationType.DTL:
        return productType === ProductType.MEDICINE &&
          medicineClassification === MedicineClassification.OBAT_BEBAS_TERBATAS;
      case BPOMRegistrationType.DBL:
        return productType === ProductType.MEDICINE &&
          medicineClassification === MedicineClassification.OBAT_BEBAS;
      case BPOMRegistrationType.AKL:
      case BPOMRegistrationType.AKD:
        return productType === ProductType.MEDICAL_DEVICE;
      case BPOMRegistrationType.PKD:
        return productType === ProductType.SUPPLEMENT ||
          productType === ProductType.COSMETIC;
      default:
        return false;
    }
  }

  /**
   * Check if product is imported
   */
  private isImportedProduct(product: any, supplier?: any): boolean {
    if (!supplier) return false;

    const supplierName = supplier.name.toLowerCase();
    const internationalKeywords = [
      'international', 'global', 'import', 'overseas', 'foreign',
      'gmbh', 'inc', 'ltd', 'corporation', 'corp', 'ag', 'sa', 'bv'
    ];

    return internationalKeywords.some(keyword => supplierName.includes(keyword));
  }
}
