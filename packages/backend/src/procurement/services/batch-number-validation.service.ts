import { Injectable, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BatchAuditService } from './batch-audit.service';
import { MedicineClassification, ProductType, BatchAuditAction } from '@prisma/client';

/**
 * Indonesian pharmaceutical batch number validation patterns
 */
export const BATCH_VALIDATION_RULES = {
  // Standard pharmaceutical batch format (2-4 letters + 4-8 digits + optional alphanumeric)
  STANDARD: /^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$/,

  // BPOM compliant format with date codes (2-4 letters + 6 digits date + 2-6 alphanumeric)
  BPOM_COMPLIANT: /^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$/,

  // Enhanced basic format (alphanumeric with hyphens/underscores, 3-20 chars)
  BASIC: /^[A-Za-z0-9\-_]{3,20}$/,

  // Controlled substance format (3 letters + 8 digits + 2 letters)
  CONTROLLED: /^[A-Z]{3}[0-9]{8}[A-Z]{2}$/,

  // Generic medicine format (GEN + 6 digits + 2-4 alphanumeric)
  GENERIC: /^GEN[0-9]{6}[A-Z0-9]{2,4}$/,

  // Import batch format (IMP + supplier code + batch)
  IMPORT: /^IMP[A-Z]{2,3}[A-Z0-9]{4,12}$/
};

/**
 * Batch number validation result interface
 */
export interface BatchValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validationLevel: 'BASIC' | 'STANDARD' | 'BPOM_COMPLIANT' | 'CONTROLLED';
  bpomCompliant: boolean;
  uniquenessCheck: {
    isUnique: boolean;
    conflictingBatches?: Array<{
      id: string;
      batchNumber: string;
      productName: string;
      supplierName: string;
      createdAt: Date;
    }>;
  };
  formatValidation: {
    passedRules: string[];
    failedRules: string[];
    recommendedFormat?: string;
  };
}

/**
 * Batch validation context for comprehensive validation
 */
export interface BatchValidationContext {
  batchNumber: string;
  productId: string;
  supplierId?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  isSubstitution?: boolean;
  originalBatchNumber?: string;
  userId?: string;
}



/**
 * Advanced batch number validation service for Indonesian pharmaceutical compliance
 */
@Injectable()
export class BatchNumberValidationService {
  constructor(
    private prisma: PrismaService,
    private batchAuditService: BatchAuditService
  ) { }

  /**
   * Comprehensive batch number validation
   * @param context Validation context with batch details
   * @returns Detailed validation result
   */
  async validateBatchNumber(context: BatchValidationContext): Promise<BatchValidationResult> {
    const { batchNumber, productId, supplierId, expiryDate, manufacturingDate } = context;

    // Initialize result
    const result: BatchValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      validationLevel: 'BASIC',
      bpomCompliant: false,
      uniquenessCheck: { isUnique: true },
      formatValidation: { passedRules: [], failedRules: [] }
    };

    try {
      // 1. Format validation
      await this.validateFormat(batchNumber, result);

      // 2. Get product details for context-specific validation
      const product = await this.prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        result.errors.push('Produk tidak ditemukan untuk validasi batch');
        result.isValid = false;
        return result;
      }

      // 3. Uniqueness validation
      await this.validateUniqueness(batchNumber, productId, supplierId, result);

      // 4. Product-specific validation
      await this.validateProductSpecific(batchNumber, product, result);

      // 5. Date alignment validation
      if (expiryDate || manufacturingDate) {
        await this.validateDateAlignment(batchNumber, expiryDate, manufacturingDate, result);
      }

      // 6. BPOM compliance validation
      await this.validateBPOMCompliance(batchNumber, product, result);

      // 7. Controlled substance validation
      if (product.medicineClassification === MedicineClassification.NARKOTIKA ||
        product.medicineClassification === MedicineClassification.PSIKOTROPIKA) {
        await this.validateControlledSubstance(batchNumber, product, result);
      }

      // Set overall validation status
      result.isValid = result.errors.length === 0;

      // Log validation audit trail
      await this.batchAuditService.logBatchValidation(
        batchNumber,
        productId,
        result,
        result.isValid,
        result.formatValidation.passedRules,
        context.userId,
        {
          supplierId,
          bpomCompliant: result.bpomCompliant,
          complianceLevel: result.validationLevel,
          complianceNotes: result.warnings.join('; ') || undefined,
        }
      );

    } catch (error) {
      result.errors.push(`Validasi batch gagal: ${error.message}`);
      result.isValid = false;

      // Log validation failure
      await this.batchAuditService.logAuditEvent({
        batchNumber,
        action: BatchAuditAction.VALIDATED,
        status: 'FAILED',
        productId,
        supplierId,
        userId: context.userId,
        errorMessage: error.message,
        message: `Validasi batch ${batchNumber} gagal: ${error.message}`,
      });
    }

    return result;
  }

  /**
   * Validate batch number format against Indonesian pharmaceutical standards
   */
  private async validateFormat(batchNumber: string, result: BatchValidationResult): Promise<void> {
    if (!batchNumber || typeof batchNumber !== 'string') {
      result.errors.push('Nomor batch harus berupa string yang valid');
      return;
    }

    // Trim and normalize
    const normalizedBatch = batchNumber.trim().toUpperCase();

    // Check against validation rules
    const ruleChecks = [
      { name: 'CONTROLLED', regex: BATCH_VALIDATION_RULES.CONTROLLED, level: 'CONTROLLED' as const },
      { name: 'BPOM_COMPLIANT', regex: BATCH_VALIDATION_RULES.BPOM_COMPLIANT, level: 'BPOM_COMPLIANT' as const },
      { name: 'STANDARD', regex: BATCH_VALIDATION_RULES.STANDARD, level: 'STANDARD' as const },
      { name: 'GENERIC', regex: BATCH_VALIDATION_RULES.GENERIC, level: 'STANDARD' as const },
      { name: 'IMPORT', regex: BATCH_VALIDATION_RULES.IMPORT, level: 'STANDARD' as const },
      { name: 'BASIC', regex: BATCH_VALIDATION_RULES.BASIC, level: 'BASIC' as const }
    ];

    let highestLevel: BatchValidationResult['validationLevel'] = 'BASIC';
    let passedAnyRule = false;

    for (const rule of ruleChecks) {
      if (rule.regex.test(normalizedBatch)) {
        result.formatValidation.passedRules.push(rule.name);
        highestLevel = rule.level;
        passedAnyRule = true;
        break; // Use the highest level rule that passes
      } else {
        result.formatValidation.failedRules.push(rule.name);
      }
    }

    if (!passedAnyRule) {
      result.errors.push('Format nomor batch tidak sesuai dengan standar farmasi Indonesia');
      result.formatValidation.recommendedFormat = 'Contoh: ABC123456 atau GEN240101AB';
    } else {
      result.validationLevel = highestLevel;
      if (highestLevel === 'BPOM_COMPLIANT' || highestLevel === 'CONTROLLED') {
        result.bpomCompliant = true;
      }
    }

    // Additional format checks
    if (batchNumber.length < 3) {
      result.errors.push('Nomor batch minimal 3 karakter');
    }
    if (batchNumber.length > 20) {
      result.errors.push('Nomor batch maksimal 20 karakter');
    }

    // Check for invalid characters
    if (!/^[A-Za-z0-9\-_]+$/.test(batchNumber)) {
      result.errors.push('Nomor batch hanya boleh mengandung huruf, angka, tanda hubung (-), dan underscore (_)');
    }
  }

  /**
   * Validate batch number uniqueness across products and suppliers
   */
  private async validateUniqueness(
    batchNumber: string,
    productId: string,
    supplierId: string | undefined,
    result: BatchValidationResult
  ): Promise<void> {
    // Check in inventory items
    const existingInventory = await this.prisma.inventoryItem.findMany({
      where: {
        batchNumber: {
          equals: batchNumber,
          mode: 'insensitive'
        },
        productId,
        isActive: true
      },
      include: {
        product: { select: { name: true } },
        supplier: { select: { name: true } }
      }
    });

    // Check in goods receipt items
    const existingGoodsReceipt = await this.prisma.goodsReceiptItem.findMany({
      where: {
        batchNumber: {
          equals: batchNumber,
          mode: 'insensitive'
        },
        productId
      },
      include: {
        product: { select: { name: true } },
        goodsReceipt: {
          include: {
            supplier: { select: { name: true } }
          }
        }
      }
    });

    const conflictingBatches = [
      ...existingInventory.map(item => ({
        id: item.id,
        batchNumber: item.batchNumber || '',
        productName: item.product.name,
        supplierName: item.supplier?.name || 'Unknown',
        createdAt: item.createdAt
      })),
      ...existingGoodsReceipt.map(item => ({
        id: item.id,
        batchNumber: item.batchNumber || '',
        productName: item.product.name,
        supplierName: item.goodsReceipt.supplier.name,
        createdAt: item.createdAt
      }))
    ];

    if (conflictingBatches.length > 0) {
      result.uniquenessCheck.isUnique = false;
      result.uniquenessCheck.conflictingBatches = conflictingBatches;

      // Different suppliers with same batch - warning
      // Same supplier with same batch - error
      const sameSupplierConflicts = conflictingBatches.filter(batch =>
        supplierId && batch.supplierName === supplierId
      );

      if (sameSupplierConflicts.length > 0) {
        result.errors.push(`Nomor batch ${batchNumber} sudah digunakan untuk produk ini dari supplier yang sama`);
      } else {
        result.warnings.push(`Nomor batch ${batchNumber} sudah digunakan untuk produk ini dari supplier lain`);
      }
    }
  }

  /**
   * Validate product-specific batch number requirements
   */
  private async validateProductSpecific(
    batchNumber: string,
    product: any,
    result: BatchValidationResult
  ): Promise<void> {
    // Medicine-specific validation
    if (product.type === ProductType.MEDICINE) {
      // Generic medicines should follow generic format
      if (product.name.toLowerCase().includes('generic') ||
        product.genericName) {
        if (!BATCH_VALIDATION_RULES.GENERIC.test(batchNumber.toUpperCase()) &&
          !BATCH_VALIDATION_RULES.BPOM_COMPLIANT.test(batchNumber.toUpperCase())) {
          result.warnings.push('Obat generik sebaiknya menggunakan format batch GEN atau format BPOM');
        }
      }

      // Imported medicines validation
      if (product.manufacturer &&
        !this.isIndonesianManufacturer(product.manufacturer)) {
        if (!BATCH_VALIDATION_RULES.IMPORT.test(batchNumber.toUpperCase()) &&
          !BATCH_VALIDATION_RULES.BPOM_COMPLIANT.test(batchNumber.toUpperCase())) {
          result.warnings.push('Obat impor sebaiknya menggunakan format batch IMP atau format BPOM');
        }
      }
    }

    // Medical device validation
    if (product.type === ProductType.MEDICAL_DEVICE) {
      // Medical devices should have traceable batch numbers
      if (batchNumber.length < 6) {
        result.warnings.push('Alat kesehatan sebaiknya memiliki nomor batch minimal 6 karakter untuk traceability');
      }
    }

    // Supplement validation
    if (product.type === ProductType.SUPPLEMENT) {
      // Supplements have more flexible batch requirements
      if (!BATCH_VALIDATION_RULES.BASIC.test(batchNumber)) {
        result.errors.push('Format nomor batch suplemen tidak valid');
      }
    }
  }

  /**
   * Validate date alignment between batch number, manufacturing date, and expiry date
   */
  private async validateDateAlignment(
    batchNumber: string,
    expiryDate: Date | undefined,
    manufacturingDate: Date | undefined,
    result: BatchValidationResult
  ): Promise<void> {
    const now = new Date();

    // Extract date from batch number if it follows BPOM format
    const dateMatch = batchNumber.match(/[A-Z]{2,4}([0-9]{6})/);
    if (dateMatch) {
      const batchDateStr = dateMatch[1]; // YYMMDD format
      const year = 2000 + parseInt(batchDateStr.substring(0, 2));
      const month = parseInt(batchDateStr.substring(2, 4)) - 1; // Month is 0-indexed
      const day = parseInt(batchDateStr.substring(4, 6));

      const batchDate = new Date(year, month, day);

      // Validate batch date is reasonable
      if (batchDate > now) {
        result.warnings.push('Tanggal dalam nomor batch tidak boleh di masa depan');
      }

      // Compare with manufacturing date if provided
      if (manufacturingDate) {
        const timeDiff = Math.abs(batchDate.getTime() - manufacturingDate.getTime());
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        if (daysDiff > 7) { // Allow 7 days tolerance
          result.warnings.push('Tanggal dalam nomor batch tidak sesuai dengan tanggal produksi');
        }
      }
    }

    // Standard expiry date validation
    if (expiryDate) {
      if (expiryDate <= now) {
        result.errors.push('Tanggal kedaluwarsa tidak boleh di masa lalu');
      }

      // Check reasonable shelf life (not more than 10 years)
      const maxExpiryDate = new Date();
      maxExpiryDate.setFullYear(maxExpiryDate.getFullYear() + 10);

      if (expiryDate > maxExpiryDate) {
        result.warnings.push('Tanggal kedaluwarsa lebih dari 10 tahun, mohon periksa kembali');
      }
    }

    // Manufacturing date validation
    if (manufacturingDate) {
      if (manufacturingDate > now) {
        result.errors.push('Tanggal produksi tidak boleh di masa depan');
      }

      // Manufacturing should be before expiry
      if (expiryDate && manufacturingDate >= expiryDate) {
        result.errors.push('Tanggal produksi harus sebelum tanggal kedaluwarsa');
      }
    }
  }

  /**
   * Validate BPOM compliance requirements
   */
  private async validateBPOMCompliance(
    batchNumber: string,
    product: any,
    result: BatchValidationResult
  ): Promise<void> {
    // Check if product requires BPOM registration
    if (product.type === ProductType.MEDICINE ||
      product.type === ProductType.MEDICAL_DEVICE) {

      // Check if BPOM number exists
      if (!product.bpomNumber) {
        result.warnings.push('Produk belum memiliki nomor registrasi BPOM');
        return;
      }

      // Validate BPOM number format
      if (!this.isValidBPOMNumber(product.bpomNumber)) {
        result.warnings.push('Format nomor BPOM tidak valid');
      }

      // For medicines, batch should follow BPOM format if registered
      if (product.type === ProductType.MEDICINE) {
        if (!BATCH_VALIDATION_RULES.BPOM_COMPLIANT.test(batchNumber.toUpperCase()) &&
          !BATCH_VALIDATION_RULES.CONTROLLED.test(batchNumber.toUpperCase())) {
          result.warnings.push('Obat terdaftar BPOM sebaiknya menggunakan format batch yang sesuai standar BPOM');
        } else {
          result.bpomCompliant = true;
        }
      }
    }
  }

  /**
   * Validate controlled substance batch requirements
   */
  private async validateControlledSubstance(
    batchNumber: string,
    product: any,
    result: BatchValidationResult
  ): Promise<void> {
    const isNarcotic = product.medicineClassification === MedicineClassification.NARKOTIKA;
    const isPsychotropic = product.medicineClassification === MedicineClassification.PSIKOTROPIKA;

    if (isNarcotic || isPsychotropic) {
      // Controlled substances must follow strict batch format
      if (!BATCH_VALIDATION_RULES.CONTROLLED.test(batchNumber.toUpperCase())) {
        result.errors.push(
          `${isNarcotic ? 'Narkotika' : 'Psikotropika'} harus menggunakan format batch khusus (3 huruf + 8 angka + 2 huruf)`
        );
      }

      // Additional controlled substance checks
      if (batchNumber.length !== 13) {
        result.errors.push('Nomor batch obat terkontrol harus tepat 13 karakter');
      }

      // Set highest validation level
      result.validationLevel = 'CONTROLLED';
      result.bpomCompliant = true;
    }
  }

  /**
   * Check if manufacturer is Indonesian
   */
  private isIndonesianManufacturer(manufacturer: string): boolean {
    const indonesianKeywords = [
      'indonesia', 'jakarta', 'surabaya', 'bandung', 'medan', 'semarang',
      'pt ', 'cv ', 'ud ', 'kimia farma', 'indofarma', 'kalbe', 'dexa',
      'sanbe', 'pharos', 'bernofarm', 'guardian', 'tempo'
    ];

    const lowerManufacturer = manufacturer.toLowerCase();
    return indonesianKeywords.some(keyword => lowerManufacturer.includes(keyword));
  }

  /**
   * Validate BPOM registration number format
   */
  private isValidBPOMNumber(bpomNumber: string): boolean {
    // BPOM number formats:
    // DKL: Obat keras (prescription drugs)
    // DTL: Obat bebas terbatas (limited OTC)
    // DBL: Obat bebas (OTC)
    // AKL: Alat kesehatan (medical devices)
    const bpomRegex = /^(DKL|DTL|DBL|AKL)[0-9]{10}[A-Z]{1}[0-9]{1}$/;
    return bpomRegex.test(bpomNumber.toUpperCase());
  }

  /**
   * Quick validation for real-time frontend feedback
   * @param batchNumber Batch number to validate
   * @param productId Product ID for context
   * @returns Simple validation result
   */
  async validateRealTime(batchNumber: string, productId: string): Promise<{
    isValid: boolean;
    message?: string;
    level: 'error' | 'warning' | 'success';
  }> {
    if (!batchNumber || batchNumber.trim().length === 0) {
      return { isValid: false, message: 'Nomor batch tidak boleh kosong', level: 'error' };
    }

    // Quick format check
    if (!BATCH_VALIDATION_RULES.BASIC.test(batchNumber)) {
      return {
        isValid: false,
        message: 'Format nomor batch tidak valid (gunakan huruf, angka, -, _)',
        level: 'error'
      };
    }

    // Quick uniqueness check
    const existingBatch = await this.prisma.inventoryItem.findFirst({
      where: {
        batchNumber: { equals: batchNumber, mode: 'insensitive' },
        productId,
        isActive: true
      }
    });

    if (existingBatch) {
      return {
        isValid: false,
        message: 'Nomor batch sudah digunakan untuk produk ini',
        level: 'error'
      };
    }

    // Check advanced format compliance
    if (BATCH_VALIDATION_RULES.BPOM_COMPLIANT.test(batchNumber.toUpperCase())) {
      return { isValid: true, message: 'Format batch sesuai standar BPOM', level: 'success' };
    }

    if (BATCH_VALIDATION_RULES.STANDARD.test(batchNumber.toUpperCase())) {
      return { isValid: true, message: 'Format batch sesuai standar farmasi', level: 'success' };
    }

    return { isValid: true, message: 'Format batch valid', level: 'success' };
  }

  /**
   * Check batch number uniqueness across products and suppliers
   * @param batchNumber Batch number to check
   * @param productId Product ID
   * @param supplierId Optional supplier ID
   * @returns Uniqueness check result
   */
  async checkUniqueness(batchNumber: string, productId: string, supplierId?: string): Promise<{
    isUnique: boolean;
    conflicts: Array<{
      source: 'inventory' | 'goods_receipt';
      productName: string;
      supplierName: string;
      createdAt: Date;
    }>;
  }> {
    const conflicts: any[] = [];

    // Check inventory items
    const inventoryConflicts = await this.prisma.inventoryItem.findMany({
      where: {
        batchNumber: { equals: batchNumber, mode: 'insensitive' },
        productId,
        isActive: true
      },
      include: {
        product: { select: { name: true } },
        supplier: { select: { name: true } }
      }
    });

    conflicts.push(...inventoryConflicts.map(item => ({
      source: 'inventory' as const,
      productName: item.product.name,
      supplierName: item.supplier?.name || 'Unknown',
      createdAt: item.createdAt
    })));

    // Check goods receipt items
    const goodsReceiptConflicts = await this.prisma.goodsReceiptItem.findMany({
      where: {
        batchNumber: { equals: batchNumber, mode: 'insensitive' },
        productId
      },
      include: {
        product: { select: { name: true } },
        goodsReceipt: {
          include: { supplier: { select: { name: true } } }
        }
      }
    });

    conflicts.push(...goodsReceiptConflicts.map(item => ({
      source: 'goods_receipt' as const,
      productName: item.product.name,
      supplierName: item.goodsReceipt.supplier.name,
      createdAt: item.createdAt
    })));

    return {
      isUnique: conflicts.length === 0,
      conflicts
    };
  }
}
