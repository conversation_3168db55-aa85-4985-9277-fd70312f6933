import { Module } from '@nestjs/common';
import { PurchaseOrderService } from './services/purchase-order.service';
import { GoodsReceiptService } from './services/goods-receipt.service';
import { TaxConfigurationService } from './services/tax-configuration.service';
import { TaxCalculationService } from './services/tax-calculation.service';
import { IndonesianTaxService } from './services/indonesian-tax.service';
import { BatchNumberValidationService } from './services/batch-number-validation.service';
import { BatchAuditService } from './services/batch-audit.service';
import { BPOMComplianceService } from './services/bpom-compliance.service';
import {
  IsBatchNumberFormatConstraint,
  IsBatchNumberUniqueConstraint,
  IsBPOMCompliantConstraint,
  IsBatchDateAlignedConstraint
} from './validators/batch-number.validator';
import { PurchaseOrderController } from './purchase-order.controller';
import { GoodsReceiptController } from './goods-receipt.controller';
import { TaxController } from './tax.controller';
import { BatchValidationController } from './batch-validation.controller';
import { BPOMComplianceController } from './bpom-compliance.controller';
import { NumberGeneratorService } from './utils/number-generator.utils';
import { PrismaModule } from '../prisma/prisma.module';
import { SuppliersModule } from '../suppliers/suppliers.module';
import { ProductsModule } from '../products/products.module';
import { InventoryModule } from '../inventory/inventory.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    PrismaModule,
    SuppliersModule,
    ProductsModule,
    InventoryModule,
    CommonModule,
  ],
  controllers: [
    PurchaseOrderController,
    GoodsReceiptController,
    TaxController,
    BatchValidationController,
    BPOMComplianceController,
  ],
  providers: [
    PurchaseOrderService,
    GoodsReceiptService,
    TaxConfigurationService,
    TaxCalculationService,
    IndonesianTaxService,
    BatchNumberValidationService,
    BatchAuditService,
    BPOMComplianceService,
    NumberGeneratorService,
    // Custom validators
    IsBatchNumberFormatConstraint,
    IsBatchNumberUniqueConstraint,
    IsBPOMCompliantConstraint,
    IsBatchDateAlignedConstraint,
  ],
  exports: [
    PurchaseOrderService,
    GoodsReceiptService,
    TaxConfigurationService,
    TaxCalculationService,
    IndonesianTaxService,
    BatchNumberValidationService,
    BatchAuditService,
    BPOMComplianceService,
    NumberGeneratorService,
  ],
})
export class ProcurementModule { }
