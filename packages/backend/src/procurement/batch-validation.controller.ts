import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  HttpCode,
  HttpStatus,
  BadRequestException,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { BatchNumberValidationService, BatchValidationContext } from './services/batch-number-validation.service';
import { BatchAuditService, BatchAuditQuery } from './services/batch-audit.service';
import { PrismaService } from '../prisma/prisma.service';
import {
  ValidateBatchNumberDto,
  RealTimeValidationDto,
  BatchUniquenessDto,
  BatchValidationResponseDto,
  RealTimeValidationResponseDto,
  UniquenessCheckResponseDto,
  BatchHistoryResponseDto,
  ValidationRulesResponseDto,
} from './dto/batch-validation.dto';

/**
 * Controller for batch number validation endpoints
 * Provides real-time validation, uniqueness checking, and batch history
 */
@Controller('api/procurement/batch-validation')
@UseGuards(JwtAuthGuard)
export class BatchValidationController {
  constructor(
    private readonly batchValidationService: BatchNumberValidationService,
    private readonly batchAuditService: BatchAuditService,
    private readonly prisma: PrismaService
  ) { }

  /**
   * Comprehensive batch number validation
   * POST /api/procurement/batch-validation/validate
   */
  @Post('validate')
  @HttpCode(HttpStatus.OK)
  async validateBatchNumber(
    @Body(ValidationPipe) dto: ValidateBatchNumberDto
  ): Promise<BatchValidationResponseDto> {
    try {
      const context: BatchValidationContext = {
        batchNumber: dto.batchNumber,
        productId: dto.productId,
        supplierId: dto.supplierId,
        expiryDate: dto.expiryDate ? new Date(dto.expiryDate) : undefined,
        manufacturingDate: dto.manufacturingDate ? new Date(dto.manufacturingDate) : undefined,
        isSubstitution: dto.isSubstitution,
        originalBatchNumber: dto.originalBatchNumber,
      };

      const result = await this.batchValidationService.validateBatchNumber(context);

      return {
        success: true,
        data: result,
        message: result.isValid
          ? 'Validasi batch berhasil'
          : 'Validasi batch gagal, periksa error dan warning'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal melakukan validasi batch number',
        error: error.message
      });
    }
  }

  /**
   * Real-time batch number validation for frontend
   * GET /api/procurement/batch-validation/real-time
   */
  @Get('real-time')
  async validateRealTime(
    @Query(ValidationPipe) query: RealTimeValidationDto
  ): Promise<RealTimeValidationResponseDto> {
    try {
      if (!query.batchNumber || !query.productId) {
        throw new BadRequestException('Batch number dan product ID wajib diisi');
      }

      const result = await this.batchValidationService.validateRealTime(
        query.batchNumber,
        query.productId
      );

      return {
        success: true,
        data: result,
        message: result.isValid ? 'Validasi berhasil' : (result.message || 'Validasi gagal')
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal melakukan validasi real-time',
        error: error.message
      });
    }
  }

  /**
   * Check batch number uniqueness
   * GET /api/procurement/batch-validation/check-uniqueness
   */
  @Get('check-uniqueness')
  async checkUniqueness(
    @Query(ValidationPipe) query: BatchUniquenessDto
  ): Promise<UniquenessCheckResponseDto> {
    try {
      if (!query.batchNumber || !query.productId) {
        throw new BadRequestException('Batch number dan product ID wajib diisi');
      }

      const result = await this.batchValidationService.checkUniqueness(
        query.batchNumber,
        query.productId,
        query.supplierId
      );

      return {
        success: true,
        data: result,
        message: result.isUnique
          ? 'Nomor batch tersedia'
          : `Nomor batch sudah digunakan (${result.conflicts.length} konflik ditemukan)`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal memeriksa keunikan batch number',
        error: error.message
      });
    }
  }

  /**
   * Get batch number history and audit trail
   * GET /api/procurement/batch-validation/history/:batchNumber
   */
  @Get('history/:batchNumber')
  async getBatchHistory(
    @Param('batchNumber') batchNumber: string
  ): Promise<BatchHistoryResponseDto> {
    try {
      if (!batchNumber) {
        throw new BadRequestException('Batch number wajib diisi');
      }

      // Get batch usage history from inventory and goods receipts
      const history = await this.getBatchUsageHistory(batchNumber);

      return {
        success: true,
        data: history,
        message: `Riwayat batch ${batchNumber} berhasil diambil`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil riwayat batch number',
        error: error.message
      });
    }
  }

  /**
   * Get batch validation rules and format examples
   * GET /api/procurement/batch-validation/rules
   */
  @Get('rules')
  async getValidationRules(): Promise<ValidationRulesResponseDto> {
    try {
      const rules = {
        formats: {
          STANDARD: {
            pattern: '^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$',
            description: 'Format standar farmasi (2-4 huruf + 4-8 angka + opsional alphanumeric)',
            example: 'ABC12345678'
          },
          BPOM_COMPLIANT: {
            pattern: '^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$',
            description: 'Format sesuai BPOM dengan kode tanggal (2-4 huruf + 6 angka tanggal + 2-6 alphanumeric)',
            example: 'KMF240615AB'
          },
          CONTROLLED: {
            pattern: '^[A-Z]{3}[0-9]{8}[A-Z]{2}$',
            description: 'Format khusus obat terkontrol (3 huruf + 8 angka + 2 huruf)',
            example: 'NAR12345678AB'
          },
          GENERIC: {
            pattern: '^GEN[0-9]{6}[A-Z0-9]{2,4}$',
            description: 'Format obat generik (GEN + 6 angka + 2-4 alphanumeric)',
            example: 'GEN240615AB'
          },
          IMPORT: {
            pattern: '^IMP[A-Z]{2,3}[A-Z0-9]{4,12}$',
            description: 'Format obat impor (IMP + kode supplier + batch)',
            example: 'IMPUSA123456'
          },
          BASIC: {
            pattern: '^[A-Za-z0-9\\-_]{3,20}$',
            description: 'Format dasar (3-20 karakter: huruf, angka, -, _)',
            example: 'BATCH-123'
          }
        },
        requirements: {
          length: { min: 3, max: 20 },
          allowedCharacters: 'Huruf (A-Z, a-z), angka (0-9), tanda hubung (-), underscore (_)',
          uniqueness: 'Harus unik per produk',
          bpomCompliance: 'Obat terdaftar BPOM sebaiknya menggunakan format BPOM_COMPLIANT atau CONTROLLED'
        },
        medicineTypes: {
          NARKOTIKA: 'Wajib menggunakan format CONTROLLED',
          PSIKOTROPIKA: 'Wajib menggunakan format CONTROLLED',
          OBAT_KERAS: 'Disarankan format BPOM_COMPLIANT atau STANDARD',
          OBAT_BEBAS: 'Dapat menggunakan format BASIC atau STANDARD',
          GENERIK: 'Disarankan format GENERIC atau BPOM_COMPLIANT',
          IMPOR: 'Disarankan format IMPORT atau BPOM_COMPLIANT'
        }
      };

      return {
        success: true,
        data: rules,
        message: 'Aturan validasi batch number berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil aturan validasi',
        error: error.message
      });
    }
  }

  /**
   * Get batch audit trail
   * GET /api/procurement/batch-validation/audit/:batchNumber
   */
  @Get('audit/:batchNumber')
  async getBatchAuditTrail(
    @Param('batchNumber') batchNumber: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string
  ) {
    try {
      if (!batchNumber) {
        throw new BadRequestException('Batch number wajib diisi');
      }

      const auditTrail = await this.batchAuditService.getBatchAuditTrail(
        batchNumber,
        limit ? parseInt(limit) : 50,
        offset ? parseInt(offset) : 0
      );

      return {
        success: true,
        data: auditTrail,
        message: `Audit trail untuk batch ${batchNumber} berhasil diambil`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil audit trail batch',
        error: error.message
      });
    }
  }

  /**
   * Query audit logs with filters
   * GET /api/procurement/batch-validation/audit-logs
   */
  @Get('audit-logs')
  async queryAuditLogs(@Query() query: any) {
    try {
      const auditQuery: BatchAuditQuery = {
        batchNumber: query.batchNumber,
        productId: query.productId,
        supplierId: query.supplierId,
        userId: query.userId,
        action: query.action,
        status: query.status,
        dateFrom: query.dateFrom ? new Date(query.dateFrom) : undefined,
        dateTo: query.dateTo ? new Date(query.dateTo) : undefined,
        limit: query.limit ? parseInt(query.limit) : 50,
        offset: query.offset ? parseInt(query.offset) : 0,
      };

      const result = await this.batchAuditService.queryAuditLogs(auditQuery);

      return {
        success: true,
        data: result,
        message: 'Audit logs berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil audit logs',
        error: error.message
      });
    }
  }

  /**
   * Get batch audit statistics
   * GET /api/procurement/batch-validation/audit-stats/:batchNumber
   */
  @Get('audit-stats/:batchNumber')
  async getBatchAuditStats(@Param('batchNumber') batchNumber: string) {
    try {
      if (!batchNumber) {
        throw new BadRequestException('Batch number wajib diisi');
      }

      const stats = await this.batchAuditService.getBatchAuditStats(batchNumber);

      return {
        success: true,
        data: stats,
        message: `Statistik audit untuk batch ${batchNumber} berhasil diambil`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil statistik audit',
        error: error.message
      });
    }
  }

  /**
   * Helper method to get comprehensive batch usage history
   */
  private async getBatchUsageHistory(batchNumber: string) {
    try {
      // Get inventory items with this batch number
      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where: {
          batchNumber: {
            equals: batchNumber,
            mode: 'insensitive'
          }
        },
        include: {
          product: { select: { name: true } },
          unit: { select: { name: true } },
          supplier: { select: { name: true } },
          stockMovements: {
            orderBy: { createdAt: 'desc' },
            take: 50 // Limit to recent movements
          }
        }
      });

      // Get goods receipt items with this batch number
      const goodsReceiptItems = await this.prisma.goodsReceiptItem.findMany({
        where: {
          batchNumber: {
            equals: batchNumber,
            mode: 'insensitive'
          }
        },
        include: {
          product: { select: { name: true } },
          unit: { select: { name: true } },
          goodsReceipt: {
            include: {
              supplier: { select: { name: true } }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Get sale items with this batch number
      const saleItems = await this.prisma.saleItem.findMany({
        where: {
          batchNumber: {
            equals: batchNumber,
            mode: 'insensitive'
          }
        },
        include: {
          product: { select: { name: true } },
          unit: { select: { name: true } },
          sale: { select: { saleNumber: true, saleDate: true } }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Compile usage history
      const usageHistory: any[] = [];

      // Add goods receipt entries
      goodsReceiptItems.forEach(item => {
        usageHistory.push({
          id: item.id,
          type: 'goods_receipt',
          productName: item.product.name,
          quantity: item.quantityReceived,
          unitName: item.unit.name,
          date: item.createdAt,
          reference: item.goodsReceipt.supplier.name,
          notes: `Penerimaan barang - ${item.notes || ''}`
        });
      });

      // Add sale entries
      saleItems.forEach(item => {
        usageHistory.push({
          id: item.id,
          type: 'sale',
          productName: item.product.name,
          quantity: -item.quantity, // Negative for outgoing
          unitName: item.unit.name,
          date: item.createdAt,
          reference: item.sale.saleNumber,
          notes: `Penjualan - ${item.notes || ''}`
        });
      });

      // Add stock movement entries
      inventoryItems.forEach(invItem => {
        invItem.stockMovements.forEach(movement => {
          usageHistory.push({
            id: movement.id,
            type: 'adjustment',
            productName: invItem.product.name,
            quantity: movement.quantity,
            unitName: invItem.unit.name,
            date: movement.createdAt,
            reference: movement.referenceNumber || 'Penyesuaian Stok',
            notes: movement.reason || movement.notes || ''
          });
        });
      });

      // Sort by date (newest first)
      usageHistory.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      // Calculate totals
      const totalReceived = goodsReceiptItems.reduce((sum, item) => sum + item.quantityReceived, 0);
      const totalSold = saleItems.reduce((sum, item) => sum + item.quantity, 0);
      const currentStock = inventoryItems.reduce((sum, item) => sum + item.quantityOnHand, 0);

      // Get latest activity
      const lastActivity = usageHistory.length > 0 ? usageHistory[0].date : null;
      const createdAt = goodsReceiptItems.length > 0 ? goodsReceiptItems[goodsReceiptItems.length - 1].createdAt : null;

      // Determine status
      let status: 'active' | 'depleted' | 'expired' | 'recalled' = 'active';
      if (currentStock === 0) {
        status = 'depleted';
      } else if (inventoryItems.some(item => item.expiryDate && item.expiryDate <= new Date())) {
        status = 'expired';
      }

      // Get expiry date and storage locations
      const expiryDate = inventoryItems.find(item => item.expiryDate)?.expiryDate || undefined;
      const storageLocations = [...new Set(inventoryItems.map(item => item.location).filter(Boolean))] as string[];

      return {
        batchNumber,
        usageHistory,
        totalReceived,
        totalUsed: totalSold,
        currentStock,
        lastActivity,
        createdAt,
        status,
        expiryDate,
        storageLocations
      };
    } catch (error) {
      console.error('Error getting batch usage history:', error);
      throw new BadRequestException('Gagal mengambil riwayat penggunaan batch');
    }
  }
}
