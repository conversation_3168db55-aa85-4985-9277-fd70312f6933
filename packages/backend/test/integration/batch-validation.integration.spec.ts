import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectSuccess } from './test-setup';
import { ProductType, MedicineClassification } from '@prisma/client';

describe('Batch Number Validation Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testSupplier: any;
  let testProduct: any;
  let testNarcoticProduct: any;
  let testUnit: any;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await testSetup.teardown();
  });

  async function setupTestData() {
    // Create test unit
    testUnit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Batch Unit',
        abbreviation: 'TBU',
        type: 'COUNT',
        isBaseUnit: true,
      },
    });

    // Create test supplier
    testSupplier = await ctx.prisma.supplier.create({
      data: {
        code: 'TEST-BATCH-SUP-001',
        name: 'Test Batch Supplier',
        type: 'PBF',
        status: 'ACTIVE',
        createdBy: ctx.users.admin.id,
      },
    });

    // Create regular medicine product
    testProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-BATCH-PROD-001',
        name: 'Test Batch Product',
        type: ProductType.MEDICINE,
        category: 'ANALGESIC',
        medicineClassification: MedicineClassification.OBAT_KERAS,
        bpomNumber: 'DKL1234567890A1',
        baseUnitId: testUnit.id,
        createdBy: ctx.users.admin.id,
      },
    });

    // Create narcotic product for controlled substance testing
    testNarcoticProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-NARCOTIC-001',
        name: 'Test Narcotic Product',
        type: ProductType.MEDICINE,
        category: 'ANALGESIC',
        medicineClassification: MedicineClassification.NARKOTIKA,
        bpomNumber: 'DKL9876543210B2',
        baseUnitId: testUnit.id,
        createdBy: ctx.users.admin.id,
      },
    });
  }

  async function cleanupTestData() {
    try {
      await ctx.prisma.batchAuditLog.deleteMany({});
      await ctx.prisma.inventoryItem.deleteMany({});
      await ctx.prisma.product.deleteMany({ 
        where: { 
          code: { 
            in: ['TEST-BATCH-PROD-001', 'TEST-NARCOTIC-001'] 
          } 
        } 
      });
      await ctx.prisma.supplier.deleteMany({ where: { code: 'TEST-BATCH-SUP-001' } });
      await ctx.prisma.productUnit.deleteMany({ where: { name: 'Test Batch Unit' } });
    } catch (error) {
      console.warn('Failed to cleanup batch validation test data:', error.message);
    }
  }

  describe('Batch Validation API (/api/procurement/batch-validation)', () => {
    describe('POST /api/procurement/batch-validation/validate', () => {
      it('should validate a valid BPOM compliant batch number', async () => {
        const validationData = {
          batchNumber: 'ABC240615XY',
          productId: testProduct?.id || 'mock-product-id',
          supplierId: testSupplier?.id || 'mock-supplier-id',
          expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          manufacturingDate: new Date().toISOString(),
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(true);
        expect(response.body.data.bpomCompliant).toBe(true);
        expect(response.body.data.validationLevel).toBe('BPOM_COMPLIANT');
        expect(response.body.data.errors).toHaveLength(0);
      });

      it('should reject invalid batch number format', async () => {
        const validationData = {
          batchNumber: 'invalid@batch#',
          productId: testProduct?.id || 'mock-product-id',
          supplierId: testSupplier?.id || 'mock-supplier-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(false);
        expect(response.body.data.errors.length).toBeGreaterThan(0);
        expect(response.body.data.errors.some((error: string) => error.includes('karakter'))).toBe(true);
      });

      it('should validate controlled substance batch format', async () => {
        const validationData = {
          batchNumber: 'NAR12345678AB',
          productId: testNarcoticProduct?.id || 'mock-narcotic-id',
          supplierId: testSupplier?.id || 'mock-supplier-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        if (testNarcoticProduct) {
          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.validationLevel).toBe('CONTROLLED');
          expect(response.body.data.bpomCompliant).toBe(true);
        } else {
          expect([400, 404]).toContain(response.status);
        }
      });

      it('should reject invalid controlled substance batch format', async () => {
        const validationData = {
          batchNumber: 'INVALID123',
          productId: testNarcoticProduct?.id || 'mock-narcotic-id',
          supplierId: testSupplier?.id || 'mock-supplier-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        if (testNarcoticProduct) {
          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.isValid).toBe(false);
          expect(response.body.data.errors.some((error: string) => error.includes('Narkotika'))).toBe(true);
        } else {
          expect([400, 404]).toContain(response.status);
        }
      });

      it('should detect duplicate batch numbers', async () => {
        if (testProduct && testSupplier) {
          // First create an inventory item with a batch number
          await ctx.prisma.inventoryItem.create({
            data: {
              productId: testProduct.id,
              unitId: testUnit.id,
              supplierId: testSupplier.id,
              batchNumber: 'DUPLICATE123',
              quantityOnHand: 100,
              costPrice: 5000,
              receivedDate: new Date(),
              createdBy: ctx.users.admin.id,
            },
          });

          // Now try to validate the same batch number
          const validationData = {
            batchNumber: 'DUPLICATE123',
            productId: testProduct.id,
            supplierId: testSupplier.id,
          };

          const response = await ctx.request
            .post('/api/procurement/batch-validation/validate')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(validationData);

          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.isValid).toBe(false);
          expect(response.body.data.uniquenessCheck.isUnique).toBe(false);
          expect(response.body.data.errors.some((error: string) => error.includes('sudah digunakan'))).toBe(true);
        }
      });

      it('should validate expiry date alignment', async () => {
        const pastDate = new Date();
        pastDate.setFullYear(pastDate.getFullYear() - 1);

        const validationData = {
          batchNumber: 'ABC240615XY',
          productId: testProduct?.id || 'mock-product-id',
          supplierId: testSupplier?.id || 'mock-supplier-id',
          expiryDate: pastDate.toISOString(),
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(false);
        expect(response.body.data.errors.some((error: string) => error.includes('masa lalu'))).toBe(true);
      });

      it('should require authentication', async () => {
        const validationData = {
          batchNumber: 'ABC240615XY',
          productId: 'test-product-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .send(validationData);

        expectUnauthorized(response);
      });

      it('should validate required fields', async () => {
        const invalidData = {
          // Missing batchNumber and productId
          supplierId: testSupplier?.id || 'mock-supplier-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/validate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(invalidData);

        expectValidationError(response);
      });
    });

    describe('POST /api/procurement/batch-validation/real-time', () => {
      it('should perform quick real-time validation', async () => {
        const validationData = {
          batchNumber: 'ABC240615XY',
          productId: testProduct?.id || 'mock-product-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/real-time')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(true);
        expect(response.body.data.level).toBe('success');
        expect(response.body.data.message).toContain('BPOM');
      });

      it('should reject empty batch number in real-time', async () => {
        const validationData = {
          batchNumber: '',
          productId: testProduct?.id || 'mock-product-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/real-time')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(validationData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isValid).toBe(false);
        expect(response.body.data.level).toBe('error');
        expect(response.body.data.message).toContain('kosong');
      });
    });

    describe('POST /api/procurement/batch-validation/check-uniqueness', () => {
      it('should check batch number uniqueness', async () => {
        const uniquenessData = {
          batchNumber: 'UNIQUE123',
          productId: testProduct?.id || 'mock-product-id',
        };

        const response = await ctx.request
          .post('/api/procurement/batch-validation/check-uniqueness')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(uniquenessData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isUnique).toBe(true);
        expect(response.body.data.conflicts).toHaveLength(0);
      });

      it('should detect conflicts when duplicates exist', async () => {
        if (testProduct && testSupplier) {
          // Create an inventory item with a batch number
          await ctx.prisma.inventoryItem.create({
            data: {
              productId: testProduct.id,
              unitId: testUnit.id,
              supplierId: testSupplier.id,
              batchNumber: 'CONFLICT123',
              quantityOnHand: 50,
              costPrice: 3000,
              receivedDate: new Date(),
              createdBy: ctx.users.admin.id,
            },
          });

          const uniquenessData = {
            batchNumber: 'CONFLICT123',
            productId: testProduct.id,
          };

          const response = await ctx.request
            .post('/api/procurement/batch-validation/check-uniqueness')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(uniquenessData);

          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.isUnique).toBe(false);
          expect(response.body.data.conflicts).toHaveLength(1);
          expect(response.body.data.conflicts[0].source).toBe('inventory');
        }
      });
    });

    describe('GET /api/procurement/batch-validation/history/:batchNumber', () => {
      it('should get batch history for existing batch', async () => {
        if (testProduct && testSupplier) {
          // Create an inventory item to have history
          await ctx.prisma.inventoryItem.create({
            data: {
              productId: testProduct.id,
              unitId: testUnit.id,
              supplierId: testSupplier.id,
              batchNumber: 'HISTORY123',
              quantityOnHand: 75,
              costPrice: 4000,
              receivedDate: new Date(),
              createdBy: ctx.users.admin.id,
            },
          });

          const response = await ctx.request
            .get('/api/procurement/batch-validation/history/HISTORY123')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data).toBeDefined();
          expect(response.body.data.batchNumber).toBe('HISTORY123');
        }
      });

      it('should return empty history for non-existent batch', async () => {
        const response = await ctx.request
          .get('/api/procurement/batch-validation/history/NONEXISTENT123')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.usageHistory).toHaveLength(0);
      });
    });
  });
});
