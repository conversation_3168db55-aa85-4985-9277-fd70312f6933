import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectSuccess } from './test-setup';
import { ProductType, MedicineClassification } from '@prisma/client';

describe('BPOM Compliance Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testSupplier: any;
  let testProduct: any;
  let testNarcoticProduct: any;
  let testImportProduct: any;
  let testUnit: any;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await testSetup.teardown();
  });

  async function setupTestData() {
    // Create test unit
    testUnit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test BPOM Unit',
        abbreviation: 'TBU',
        type: 'COUNT',
        isBaseUnit: true,
      },
    });

    // Create domestic supplier
    testSupplier = await ctx.prisma.supplier.create({
      data: {
        code: 'TEST-BPOM-SUP-001',
        name: 'PT Test Pharma Indonesia',
        type: 'PBF',
        status: 'ACTIVE',
        createdBy: ctx.users.admin.id,
      },
    });

    // Create international supplier for import testing
    const internationalSupplier = await ctx.prisma.supplier.create({
      data: {
        code: 'TEST-IMPORT-SUP-001',
        name: 'Global Pharma International Inc',
        type: 'PBF',
        status: 'ACTIVE',
        createdBy: ctx.users.admin.id,
      },
    });

    // Create regular medicine product with BPOM registration
    testProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-BPOM-PROD-001',
        name: 'Test BPOM Product',
        type: ProductType.MEDICINE,
        category: 'ANALGESIC',
        medicineClassification: MedicineClassification.OBAT_KERAS,
        bpomNumber: 'DKL1234567890A1',
        baseUnitId: testUnit.id,
        createdBy: ctx.users.admin.id,
      },
    });

    // Create narcotic product for controlled substance testing
    testNarcoticProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-NARCOTIC-BPOM-001',
        name: 'Test Narcotic BPOM Product',
        type: ProductType.MEDICINE,
        category: 'ANALGESIC',
        medicineClassification: MedicineClassification.NARKOTIKA,
        bpomNumber: 'DKL9876543210B2',
        baseUnitId: testUnit.id,
        createdBy: ctx.users.admin.id,
      },
    });

    // Create import product
    testImportProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-IMPORT-PROD-001',
        name: 'Test Import Product',
        type: ProductType.MEDICINE,
        category: 'ANTIBIOTIC',
        medicineClassification: MedicineClassification.OBAT_KERAS,
        bpomNumber: 'DKL5555666677A1',
        baseUnitId: testUnit.id,
        createdBy: ctx.users.admin.id,
      },
    });

    // Create inventory items for testing
    await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProduct.id,
        unitId: testUnit.id,
        supplierId: testSupplier.id,
        batchNumber: 'BPOM240615XY',
        quantityOnHand: 100,
        costPrice: 5000,
        receivedDate: new Date(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        createdBy: ctx.users.admin.id,
      },
    });

    await ctx.prisma.inventoryItem.create({
      data: {
        productId: testNarcoticProduct.id,
        unitId: testUnit.id,
        supplierId: testSupplier.id,
        batchNumber: 'NAR12345678AB',
        quantityOnHand: 50,
        costPrice: 10000,
        receivedDate: new Date(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        createdBy: ctx.users.admin.id,
      },
    });
  }

  async function cleanupTestData() {
    try {
      await ctx.prisma.batchAuditLog.deleteMany({});
      await ctx.prisma.inventoryItem.deleteMany({});
      await ctx.prisma.product.deleteMany({ 
        where: { 
          code: { 
            in: ['TEST-BPOM-PROD-001', 'TEST-NARCOTIC-BPOM-001', 'TEST-IMPORT-PROD-001'] 
          } 
        } 
      });
      await ctx.prisma.supplier.deleteMany({ 
        where: { 
          code: { 
            in: ['TEST-BPOM-SUP-001', 'TEST-IMPORT-SUP-001'] 
          } 
        } 
      });
      await ctx.prisma.productUnit.deleteMany({ where: { name: 'Test BPOM Unit' } });
    } catch (error) {
      console.warn('Failed to cleanup BPOM compliance test data:', error.message);
    }
  }

  describe('BPOM Compliance API (/api/procurement/bpom-compliance)', () => {
    describe('POST /api/procurement/bpom-compliance/check', () => {
      it('should perform BPOM compliance check for regular medicine', async () => {
        const complianceData = {
          productId: testProduct?.id || 'mock-product-id',
          batchNumber: 'BPOM240615XY',
          userId: ctx.users.admin.id,
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/check')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(complianceData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.isCompliant).toBeDefined();
        expect(response.body.data.complianceLevel).toBeDefined();
        expect(response.body.data.registrationValid).toBeDefined();
        expect(response.body.data.registrationType).toBe('DKL');
      });

      it('should perform compliance check for controlled substance', async () => {
        const complianceData = {
          productId: testNarcoticProduct?.id || 'mock-narcotic-id',
          batchNumber: 'NAR12345678AB',
          userId: ctx.users.admin.id,
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/check')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(complianceData);

        if (testNarcoticProduct) {
          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.controlledSubstance).toBe(true);
          expect(response.body.data.complianceLevel).toBe('CONTROLLED');
          expect(response.body.data.recommendations).toContain(
            expect.stringContaining('obat terkontrol')
          );
        } else {
          expect([400, 404]).toContain(response.status);
        }
      });

      it('should detect non-compliant batch format', async () => {
        const complianceData = {
          productId: testProduct?.id || 'mock-product-id',
          batchNumber: 'INVALID@BATCH#',
          userId: ctx.users.admin.id,
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/check')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(complianceData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.warnings.length).toBeGreaterThan(0);
      });

      it('should require authentication', async () => {
        const complianceData = {
          productId: 'test-product-id',
          batchNumber: 'TEST123',
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/check')
          .send(complianceData);

        expectUnauthorized(response);
      });

      it('should validate required fields', async () => {
        const invalidData = {
          // Missing productId and batchNumber
          userId: ctx.users.admin.id,
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/check')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(invalidData);

        expectValidationError(response);
      });
    });

    describe('GET /api/procurement/bpom-compliance/batch-status', () => {
      it('should get compliance status for multiple batches', async () => {
        const response = await ctx.request
          .get('/api/procurement/bpom-compliance/batch-status?limit=10')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.results).toBeInstanceOf(Array);
        expect(response.body.data.summary).toBeDefined();
        expect(response.body.data.summary.compliant).toBeGreaterThanOrEqual(0);
        expect(response.body.data.summary.nonCompliant).toBeGreaterThanOrEqual(0);
      });

      it('should filter by product IDs', async () => {
        if (testProduct) {
          const response = await ctx.request
            .get(`/api/procurement/bpom-compliance/batch-status?productIds=${testProduct.id}`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.results).toBeInstanceOf(Array);
          // All results should be for the specified product
          response.body.data.results.forEach((result: any) => {
            expect(result.productId).toBe(testProduct.id);
          });
        }
      });

      it('should filter by batch numbers', async () => {
        const response = await ctx.request
          .get('/api/procurement/bpom-compliance/batch-status?batchNumbers=BPOM240615XY,NAR12345678AB')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.results).toBeInstanceOf(Array);
      });
    });

    describe('GET /api/procurement/bpom-compliance/controlled-substances', () => {
      it('should get controlled substance tracking data', async () => {
        const response = await ctx.request
          .get('/api/procurement/bpom-compliance/controlled-substances')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toBeInstanceOf(Array);
        expect(response.body.data.summary).toBeDefined();
        expect(response.body.data.summary.totalItems).toBeGreaterThanOrEqual(0);
        expect(response.body.data.summary.narkotika).toBeGreaterThanOrEqual(0);
        expect(response.body.data.summary.psikotropika).toBeGreaterThanOrEqual(0);
      });

      it('should filter controlled substances by classification', async () => {
        const response = await ctx.request
          .get('/api/procurement/bpom-compliance/controlled-substances?classification=NARKOTIKA')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toBeInstanceOf(Array);
        // All items should be NARKOTIKA
        response.body.data.items.forEach((item: any) => {
          expect(item.classification).toBe('NARKOTIKA');
        });
      });

      it('should filter controlled substances by date range', async () => {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        const endDate = new Date();

        const response = await ctx.request
          .get(`/api/procurement/bpom-compliance/controlled-substances?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.items).toBeInstanceOf(Array);
      });
    });

    describe('POST /api/procurement/bpom-compliance/inspection-prep', () => {
      it('should generate BPOM inspection preparation report', async () => {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        const endDate = new Date();

        const inspectionData = {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          includeControlledSubstances: true,
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/inspection-prep')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(inspectionData);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.period).toBeDefined();
        expect(response.body.data.summary).toBeDefined();
        expect(response.body.data.summary.totalBatches).toBeGreaterThanOrEqual(0);
        expect(response.body.data.complianceIssues).toBeInstanceOf(Array);
        expect(response.body.data.controlledSubstanceReport).toBeInstanceOf(Array);
        expect(response.body.data.preparationChecklist).toBeInstanceOf(Array);
      });

      it('should filter inspection report by product IDs', async () => {
        if (testProduct) {
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - 30);
          const endDate = new Date();

          const inspectionData = {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            productIds: [testProduct.id],
          };

          const response = await ctx.request
            .post('/api/procurement/bpom-compliance/inspection-prep')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(inspectionData);

          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.summary).toBeDefined();
        }
      });

      it('should validate required date fields', async () => {
        const invalidData = {
          // Missing startDate and endDate
          includeControlledSubstances: true,
        };

        const response = await ctx.request
          .post('/api/procurement/bpom-compliance/inspection-prep')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(invalidData);

        expectValidationError(response);
      });
    });

    describe('GET /api/procurement/bpom-compliance/statistics', () => {
      it('should get BPOM compliance statistics', async () => {
        const response = await ctx.request
          .get('/api/procurement/bpom-compliance/statistics')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body.success).toBe(true);
        expect(response.body.data.totalBatches).toBeGreaterThanOrEqual(0);
        expect(response.body.data.bpomRegistered).toBeGreaterThanOrEqual(0);
        expect(response.body.data.controlledSubstances).toBeGreaterThanOrEqual(0);
        expect(response.body.data.medicines).toBeGreaterThanOrEqual(0);
        expect(response.body.data.complianceRate).toBeGreaterThanOrEqual(0);
        expect(response.body.data.period).toBeDefined();
      });

      it('should get statistics for different time periods', async () => {
        const periods = ['7d', '30d', '90d', '1y'];

        for (const period of periods) {
          const response = await ctx.request
            .get(`/api/procurement/bpom-compliance/statistics?period=${period}`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(response);
          expect(response.body.success).toBe(true);
          expect(response.body.data.period.days).toBeGreaterThan(0);
        }
      });
    });
  });
});
