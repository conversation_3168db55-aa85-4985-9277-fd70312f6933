import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { NewPurchaseOrderPageClient } from '@/components/purchase-orders/NewPurchaseOrderPageClient';

export const metadata: Metadata = {
  title: 'Buat Purchase Order Baru - Apotek App',
  description: 'Buat purchase order baru untuk pemesanan ke supplier',
};

export default async function NewPurchaseOrderPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <NewPurchaseOrderPageClient />
          </div>
        </div>
      </div>
    </>
  );
}
