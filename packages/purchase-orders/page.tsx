import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { PurchaseOrdersPageClient } from '@/components/purchase-orders/PurchaseOrdersPageClient';
import { PurchaseOrderStatsCards } from '@/components/purchase-orders/purchase-order-stats-cards';
import { PurchaseOrderQueryParams } from '@/types/purchase-order';
import { DEFAULT_PURCHASE_ORDER_PAGINATION } from '@/lib/constants/purchase-order';

export const metadata: Metadata = {
  title: 'Purchase Orders - Apotek App',
  description: 'Kelola purchase order dan pemesanan ke supplier',
};

interface PurchaseOrdersPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function PurchaseOrdersPage({ searchParams }: PurchaseOrdersPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  // Parse search parameters
  const resolvedSearchParams = await searchParams;
  
  const filters: PurchaseOrderQueryParams = {
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page as string) : DEFAULT_PURCHASE_ORDER_PAGINATION.page,
    limit: resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit as string) : DEFAULT_PURCHASE_ORDER_PAGINATION.limit,
    search: resolvedSearchParams.search as string,
    supplierId: resolvedSearchParams.supplierId as string,
    status: resolvedSearchParams.status as any,
    orderDateFrom: resolvedSearchParams.orderDateFrom as string,
    orderDateTo: resolvedSearchParams.orderDateTo as string,
    expectedDeliveryFrom: resolvedSearchParams.expectedDeliveryFrom as string,
    expectedDeliveryTo: resolvedSearchParams.expectedDeliveryTo as string,
    createdBy: resolvedSearchParams.createdBy as string,
    sortBy: (resolvedSearchParams.sortBy as string) || DEFAULT_PURCHASE_ORDER_PAGINATION.sortBy,
    sortOrder: (resolvedSearchParams.sortOrder as 'asc' | 'desc') || DEFAULT_PURCHASE_ORDER_PAGINATION.sortOrder,
  };

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards */}
          <div className="px-4 lg:px-6">
            <PurchaseOrderStatsCards />
          </div>

          {/* Client-side interactive components */}
          <div className="px-4 lg:px-6">
            <PurchaseOrdersPageClient
              initialQuery={filters}
            />
          </div>
        </div>
      </div>
    </>
  );
}
