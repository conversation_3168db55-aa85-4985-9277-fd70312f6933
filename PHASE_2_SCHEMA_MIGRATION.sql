-- Phase 2: Product Substitution Enhancement Migration
-- Add product substitution tracking fields to GoodsReceiptItem

-- Add substitution tracking fields
ALTER TABLE goods_receipt_items 
ADD COLUMN is_substitution BOOLEAN DEFAULT FALSE,
ADD COLUMN original_product_id VARCHAR(255),
ADD COLUMN substitution_reason VARCHAR(255),
ADD COLUMN substitution_approved_by VARCHAR(255),
ADD COLUMN substitution_approved_at TIMESTAMP,
ADD COLUMN substitution_notes TEXT;

-- Add foreign key constraint for original_product_id
ALTER TABLE goods_receipt_items 
ADD CONSTRAINT fk_goods_receipt_items_original_product 
FOREIGN KEY (original_product_id) REFERENCES products(id);

-- Add foreign key constraint for substitution_approved_by
ALTER TABLE goods_receipt_items 
ADD CONSTRAINT fk_goods_receipt_items_substitution_approved_by 
FOREIGN KEY (substitution_approved_by) REFERENCES users(id);

-- Create index for substitution queries
CREATE INDEX idx_goods_receipt_items_substitution ON goods_receipt_items(is_substitution);
CREATE INDEX idx_goods_receipt_items_substitution_reason ON goods_receipt_items(substitution_reason);

-- Add comments for documentation
COMMENT ON COLUMN goods_receipt_items.is_substitution IS 'Indicates if this item is a substitution from the original PO';
COMMENT ON COLUMN goods_receipt_items.original_product_id IS 'Original product ID from purchase order (if substituted)';
COMMENT ON COLUMN goods_receipt_items.substitution_reason IS 'Reason for product substitution';
COMMENT ON COLUMN goods_receipt_items.substitution_approved_by IS 'User who approved the substitution';
COMMENT ON COLUMN goods_receipt_items.substitution_approved_at IS 'Timestamp when substitution was approved';
COMMENT ON COLUMN goods_receipt_items.substitution_notes IS 'Additional notes about the substitution';
